package middlewares

import (
	"errors"
	"net/http"

	_errors "github.com/dsoplabs/dinbora-backend/internal/errors"
	"github.com/labstack/echo/v4"
)

type (
	httpErrorHandler struct {
		statusCodes map[error]int
	}
)

var errorsMap = make(map[error]int)

func RegisterError(err error, status int) bool {
	ok := false

	errorsMap[err] = status
	_, ok = errorsMap[err]

	return ok
}

func NewHttpErrorHandler() *httpErrorHandler {
	return &httpErrorHandler{
		statusCodes: errorsMap,
	}
}

func (h *httpErrorHandler) getStatusCode(err error) int {
	for key, value := range h.statusCodes {
		if errors.Is(err, key) {
			return value
		}
	}

	return http.StatusInternalServerError
}

func unwrapRecursive(err error) error {
	var originalErr = err

	for originalErr != nil {
		var internalErr = errors.Unwrap(originalErr)

		if internalErr == nil {
			break
		}

		originalErr = internalErr
	}

	return originalErr
}

func (h *httpError<PERSON>and<PERSON>) Handler(err error, c echo.Context) {
	he, ok := err.(*echo.HTTPError)
	if ok {
		if he.Internal != nil {
			if herr, ok := he.Internal.(*echo.HTTPError); ok {
				he = herr
			}
		}
	} else if de, ok := err.(*_errors.DomainError); ok {
		restErr := _errors.HTTPStatus(de)
		he = &echo.HTTPError{
			Code:    restErr.Code(),
			Message: restErr.Message(),
		}
	} else {
		he = &echo.HTTPError{
			Code:    h.getStatusCode(err),
			Message: unwrapRecursive(err).Error(),
		}
	}

	code := he.Code
	message := he.Message

	var result = make(map[string]interface{})
	// Removing the string check since echo has a debugger to deal with. Avaliate if we should put it again in the future.
	//if _, ok := he.Message.(string); ok {
	result["code"] = code
	result["message"] = message
	//}

	// Send response
	if !c.Response().Committed {
		if c.Request().Method == http.MethodHead {
			err = c.NoContent(he.Code)
		} else {
			err = c.JSON(code, result)
		}
		if err != nil {
			c.Echo().Logger.Error(err)
		}
	}
}
