package billing

import (
	"context"

	"github.com/dsoplabs/dinbora-backend/internal/model/billing"
	_billing "github.com/dsoplabs/dinbora-backend/internal/repository/billing"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// Service defines the billing service interface
type Service interface {
	PlanService
	SubscriptionService
	PaymentService
	WebhookService
}

// PlanService defines plan-related operations
type PlanService interface {
	// Plan CRUD operations
	CreatePlan(ctx context.Context, plan *billing.Plan) (string, error)
	FindPlan(ctx context.Context, id primitive.ObjectID) (*billing.Plan, error)
	FindAllPlans(ctx context.Context) ([]*billing.Plan, error)
	FindActivePlans(ctx context.Context) ([]*billing.Plan, error)
	UpdatePlan(ctx context.Context, plan *billing.Plan) error
	DeletePlan(ctx context.Context, id primitive.ObjectID) error

	// Plan lookup operations
	FindPlanByHotmartProductID(ctx context.Context, hotmartProductID string) (*billing.Plan, error)
	FindPlanByAppleProductID(ctx context.Context, appleProductID string) (*billing.Plan, error)
}

// SubscriptionService defines subscription-related operations
type SubscriptionService interface {
	// Subscription CRUD operations
	CreateSubscription(ctx context.Context, userID, planID primitive.ObjectID, provider billing.PaymentProvider) (*billing.Subscription, error)
	FindSubscription(ctx context.Context, id primitive.ObjectID) (*billing.Subscription, error)
	FindUserSubscriptions(ctx context.Context, userID primitive.ObjectID) ([]*billing.Subscription, error)
	FindActiveUserSubscriptions(ctx context.Context, userID primitive.ObjectID) ([]*billing.Subscription, error)
	UpdateSubscription(ctx context.Context, subscription *billing.Subscription) error
	DeleteSubscription(ctx context.Context, id primitive.ObjectID) error

	// Subscription management operations
	CancelSubscription(ctx context.Context, subscriptionID primitive.ObjectID, reason string) error
	SuspendSubscription(ctx context.Context, subscriptionID primitive.ObjectID) error
	ReactivateSubscription(ctx context.Context, subscriptionID primitive.ObjectID) error
	RenewSubscription(ctx context.Context, subscriptionID primitive.ObjectID) error

	// Subscription lookup operations
	FindSubscriptionByProviderID(ctx context.Context, provider billing.PaymentProvider, providerSubscriptionID string) (*billing.Subscription, error)
	FindExpiringSubscriptions(ctx context.Context, days int) ([]*billing.Subscription, error)

	// Access control operations
	HasActiveSubscription(ctx context.Context, userID primitive.ObjectID) (bool, error)
	GetUserPermissions(ctx context.Context, userID primitive.ObjectID) ([]string, error)
	GetUserFeatures(ctx context.Context, userID primitive.ObjectID) ([]string, error)
	CheckUserAccess(ctx context.Context, userID primitive.ObjectID, permission string) (bool, error)
}

// PaymentService defines payment-related operations
type PaymentService interface {
	// Payment CRUD operations
	CreatePayment(ctx context.Context, payment *billing.Payment) (string, error)
	FindPayment(ctx context.Context, id primitive.ObjectID) (*billing.Payment, error)
	FindUserPayments(ctx context.Context, userID primitive.ObjectID) ([]*billing.Payment, error)
	FindSubscriptionPayments(ctx context.Context, subscriptionID primitive.ObjectID) ([]*billing.Payment, error)
	UpdatePayment(ctx context.Context, payment *billing.Payment) error

	// Payment processing operations
	ProcessPayment(ctx context.Context, userID primitive.ObjectID, subscriptionID *primitive.ObjectID, provider billing.PaymentProvider, providerTransactionID string, amount int64, currency string, webhookData interface{}) (*billing.Payment, error)
	MarkPaymentCompleted(ctx context.Context, paymentID primitive.ObjectID) error
	MarkPaymentFailed(ctx context.Context, paymentID primitive.ObjectID, reason string) error
	MarkPaymentRefunded(ctx context.Context, paymentID primitive.ObjectID) error

	// Payment lookup operations
	FindPaymentByProviderTransactionID(ctx context.Context, provider billing.PaymentProvider, providerTransactionID string) (*billing.Payment, error)
	FindPaymentsByDateRange(ctx context.Context, startDate, endDate string) ([]*billing.Payment, error)
}

// WebhookService defines webhook-related operations
type WebhookService interface {
	// Hotmart webhook handlers
	ProcessHotmartWebhook(ctx context.Context, webhookData interface{}) error
	HandleHotmartSubscriptionCreated(ctx context.Context, webhookData interface{}) error
	HandleHotmartSubscriptionCancelled(ctx context.Context, webhookData interface{}) error
	HandleHotmartPaymentCompleted(ctx context.Context, webhookData interface{}) error
	HandleHotmartPaymentRefunded(ctx context.Context, webhookData interface{}) error

	// Apple Pay webhook handlers
	ProcessApplePayWebhook(ctx context.Context, webhookData interface{}) error
	HandleApplePaySubscriptionCreated(ctx context.Context, webhookData interface{}) error
	HandleApplePaySubscriptionCancelled(ctx context.Context, webhookData interface{}) error
	HandleApplePayPaymentCompleted(ctx context.Context, webhookData interface{}) error
	HandleApplePayPaymentRefunded(ctx context.Context, webhookData interface{}) error
}

// service implements the billing service
type service struct {
	repo _billing.Repository
}

// New creates a new billing service
func New(repo _billing.Repository) Service {
	return &service{
		repo: repo,
	}
}
