package trophy

import (
	"context"
	"github.com/dsoplabs/dinbora-backend/internal/model/content"
)

type Reader interface {
	Find(ctx context.Context, id string) (*content.Trophy, error)
	FindByIdentifier(ctx context.Context, identifier string) (*content.Trophy, error)
	FindByRequirement(ctx context.Context, requirement string) (*content.Trophy, error)
	FindAll(ctx context.Context) ([]*content.Trophy, error)
}

type Writer interface {
	Create(ctx context.Context, trophy *content.Trophy) error
	Update(ctx context.Context, trophy *content.Trophy) error
	Delete(ctx context.Context, id string) error
}

type Repository interface {
	Reader
	Writer
}
