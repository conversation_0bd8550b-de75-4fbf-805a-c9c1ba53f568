package dreamboard

import (
	"log"
	"net/http"
	"strconv"
	"strings"

	"github.com/dsoplabs/dinbora-backend/internal/api/token"
	"github.com/dsoplabs/dinbora-backend/internal/errors"
	"github.com/dsoplabs/dinbora-backend/internal/model/dreamboard"
	"github.com/labstack/echo/v4"
)

// Dream CRUD
func (dc *controller) CreateDream() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()
		userToken, err := token.GetClaimsFromRequest(c.Request())
		if err != nil {
			return err
		}

		board, err := dc.Service.FindByUser(ctx, userToken.Uid)
		if err != nil {
			return err
		}

		var dream dreamboard.Dream
		if err := c.Bind(&dream); err != nil {
			return errors.New(errors.Controller, "invalid input", errors.Validation, err)
		}

		// User creator ID is always the user creating the dreamboard
		dream.CreatorUserID = userToken.Uid

		createdDream, err := dc.Service.CreateDream(ctx, board, &dream)
		if err != nil {
			return err
		}

		return c.JSON(http.StatusCreated, createdDream)
	}
}

func (dc *controller) FindDream() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()
		dreamID, err := getParam(c, "id")
		if err != nil {
			return err
		}

		userToken, err := token.GetClaimsFromRequest(c.Request())
		if err != nil {
			return err
		}

		board, err := dc.Service.FindByUser(ctx, userToken.Uid)
		if err != nil {
			return err
		}

		dream, err := dc.Service.FindDream(ctx, board, dreamID)
		if err != nil {
			return err
		}

		return c.JSON(http.StatusOK, dream)
	}
}

func (dc *controller) FindAllDreams() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()
		userToken, err := token.GetClaimsFromRequest(c.Request())
		if err != nil {
			return err
		}

		board, err := dc.Service.FindByUser(ctx, userToken.Uid)
		if err != nil {
			return err
		}

		// Get optional query parameters
		category, _ := getParam(c, "category")
		log.Println("category", category)
		title, _ := getParam(c, "title")
		timeFrame, _ := getParam(c, "timeFrame")
		completed, _ := getParam(c, "completed")
		limitStr, _ := getParam(c, "limit")
		reverseDreams, _ := getParam(c, "reverse")
		dreamType, _ := getParam(c, "type")

		// Convert limit to an integer
		limit := 0
		if limitStr != "" {
			limit, _ = strconv.Atoi(limitStr)
		}

		// Return personal and shared dreams
		if dreamType == "" {
			personalDreams, err := dc.Service.FindPersonalDreams(ctx, userToken.Uid)
			if err != nil {
				return err
			}
			sharedDreams, err := dc.Service.FindSharedDreams(ctx, userToken.Uid)
			if err != nil {
				return err
			}
			board.Dreams = append(personalDreams, sharedDreams...)
		}

		// Filter dreams based on type parameter first
		if dreamType != "" {
			switch dreamType {
			case "personal":
				personalDreams, err := dc.Service.FindPersonalDreams(ctx, userToken.Uid)
				if err != nil {
					return err
				}
				board.Dreams = personalDreams
			case "shared":
				sharedDreams, err := dc.Service.FindSharedDreams(ctx, userToken.Uid)
				if err != nil {
					return err
				}
				board.Dreams = sharedDreams
			default:
				return errors.New(errors.Controller, "invalid type parameter. Must be 'personal' or 'shared'", errors.Validation, nil)
			}
		}

		// Filter dreams based on query parameters
		filteredDreams := []*dreamboard.Dream{}
		for _, dream := range board.Dreams {
			if category != "" && dream.Category.String() != category {
				continue
			}

			if title != "" && !strings.Contains(strings.ToLower(dream.Title), strings.ToLower(title)) {
				continue
			}

			if timeFrame != "" && string(dream.TimeFrame.String()) != timeFrame {
				continue
			}

			if completed != "" {
				isCompleted, _ := strconv.ParseBool(completed)
				if dream.Completed != isCompleted {
					continue
				}
			}
			filteredDreams = append(filteredDreams, dream)
		}

		// Reverse the order of dreams
		if reverseDreams == "true" {
			reverse(filteredDreams)
		}

		// Apply limit (if set and within range)
		if limit > 0 && limit < len(filteredDreams) {
			filteredDreams = filteredDreams[:limit]
		}

		board.Dreams = filteredDreams

		return c.JSON(http.StatusOK, board.Dreams)
	}
}

// FindPersonalDreams retrieves all personal (non-shared) dreams for the authenticated user
func (dc *controller) FindPersonalDreams() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()
		userToken, err := token.GetClaimsFromRequest(c.Request())
		if err != nil {
			return err
		}

		personalDreams, err := dc.Service.FindPersonalDreams(ctx, userToken.Uid)
		if err != nil {
			return err
		}

		return c.JSON(http.StatusOK, personalDreams)
	}
}

// FindSharedDreams retrieves all shared dreams where the user is creator or active contributor
func (dc *controller) FindSharedDreams() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()
		userToken, err := token.GetClaimsFromRequest(c.Request())
		if err != nil {
			return err
		}

		sharedDreams, err := dc.Service.FindSharedDreams(ctx, userToken.Uid)
		if err != nil {
			return err
		}

		return c.JSON(http.StatusOK, sharedDreams)
	}
}

// FindDreamDetails retrieves comprehensive dashboard information for a shared dream
func (dc *controller) FindDreamDetails() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()
		dreamID := c.Param("dreamId") // This comes from the path e.g. /dreamboards/dreams/details/:dreamId
		userToken, err := token.GetClaimsFromRequest(c.Request())
		if err != nil {
			return err
		}

		if dreamID == "" {
			return errors.New(errors.Controller, "dream ID is required", errors.BadRequest, nil)
		}

		dreamDetails, err := dc.Service.FindDreamDetails(ctx, dreamID, userToken.Uid)
		if err != nil {
			return err
		}

		// If the dream is shared and has contributors, enrich their details
		if dreamDetails.Dream.ID != "" { // Check if dream details were populated
			// The service layer now handles if it's shared or personal for contributors list.
			// If dashboard.Contributors is not nil and has items, proceed.
			if len(dreamDetails.Contributors) > 0 {
				for i := range dreamDetails.Contributors {
					// It's safer to work with a pointer or reassign after modification if not using a pointer.
					// Here, dashboard.Contributors[i] is a struct, so we modify a copy unless we get a pointer.
					// However, since we are iterating with `range dashboard.Contributors`, `dashboard.Contributors[i]` gives us the element.
					// To modify the original slice elements, we need to access them by index.
					contributor := &dreamDetails.Contributors[i] // Get a pointer to modify the actual element in the slice

					userDetails, err := dc.UserService.Find(ctx, contributor.UserID)
					if err != nil {
						// Log the error and continue. The UserName and UserAvatarURL will remain empty for this contributor.
						log.Printf("Error fetching user details for contributor %s in dream %s: %v. Proceeding with empty user details for this contributor.", contributor.UserID, dreamID, err)
						// Optionally, you could decide to return an error for the whole request:
						// return errors.New(errors.Controller, fmt.Sprintf("failed to fetch user details for contributor %s", contributor.UserID), errors.Internal, err)
						// For now, we log and continue as per common practice for dashboard displays.
						continue
					}
					contributor.UserName = userDetails.Name
					contributor.UserAvatarURL = userDetails.PhotoURL

					// Enrich recent activity with user details
					for j := range dreamDetails.RecentActivity {
						activity := &dreamDetails.RecentActivity[j]
						if activity.UserID == contributor.UserID {
							activity.UserName = userDetails.Name
							activity.UserAvatarURL = userDetails.PhotoURL
						}
					}
				}
			}
		}

		return c.JSON(http.StatusOK, dreamDetails)
	}
}

func (dc *controller) UpdateDream() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()
		dreamID, err := getParam(c, "id")
		if err != nil {
			return err
		}

		userToken, err := token.GetClaimsFromRequest(c.Request())
		if err != nil {
			return err
		}

		board, err := dc.Service.FindByUser(ctx, userToken.Uid)
		if err != nil {
			return err
		}

		var dream dreamboard.Dream
		if err := c.Bind(&dream); err != nil {
			return errors.New(errors.Controller, "invalid input", errors.Validation, err)
		}
		dream.ID = dreamID

		// Authorization: Only admins or the dreamboard owner can update dreams
		if userToken.Role != "admin" && userToken.Uid != board.User {
			return errors.New(errors.Controller, "you can only update your own dreams", errors.Forbidden, nil)
		}

		updatedDreams, err := dc.Service.UpdateDream(ctx, board, &dream)
		if err != nil {
			return err
		}

		return c.JSON(http.StatusOK, updatedDreams)
	}
}

// PatchDream handles PATCH requests to partially update a dream
func (dc *controller) PatchDream() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()

		// Get dream ID from URL
		dreamID, err := getParam(c, "id")
		if err != nil {
			return err
		}

		// Get user session token
		userToken, err := token.GetClaimsFromRequest(c.Request())
		if err != nil {
			return err
		}

		// Get the user's board
		board, err := dc.Service.FindByUser(ctx, userToken.Uid)
		if err != nil {
			return err
		}

		// Get the current dream
		currentDream, err := dc.Service.FindDream(ctx, board, dreamID)
		if err != nil {
			return err
		}

		// Bind the patch data
		var patch dreamboard.Dream
		if err := c.Bind(&patch); err != nil {
			return errors.New(errors.Controller, "invalid input", errors.Validation, err)
		}
		patch.ID = dreamID

		// Merge the patch into current dream
		mergedDream := mergeDreams(currentDream, &patch)

		// Authorization check
		if userToken.Role != "admin" && userToken.Uid != board.User {
			return errors.New(errors.Controller, "you can only update your own dreams", errors.Forbidden, nil)
		}

		// Update using the merged dream
		updatedDreams, err := dc.Service.UpdateDream(ctx, board, mergedDream)
		if err != nil {
			return err
		}

		return c.JSON(http.StatusOK, updatedDreams)
	}
}

func (dc *controller) RemoveDream() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()
		dreamID, err := getParam(c, "id")
		if err != nil {
			return err
		}

		userToken, err := token.GetClaimsFromRequest(c.Request())
		if err != nil {
			return err
		}

		board, err := dc.Service.FindByUser(ctx, userToken.Uid)
		if err != nil {
			return err
		}

		var dream dreamboard.Dream
		if err := c.Bind(&dream); err != nil {
			return errors.New(errors.Controller, "invalid input", errors.Validation, err)
		}
		dream.ID = dreamID

		// Authorization: Only admins or the dreamboard owner can delete dreams
		if userToken.Role != "admin" && userToken.Uid != board.User {
			return errors.New(errors.Controller, "you can only delete your own dreams", errors.Forbidden, nil)
		}

		updatedDreams, err := dc.Service.RemoveDream(ctx, board, &dream)
		if err != nil {
			return err
		}

		return c.JSON(http.StatusOK, updatedDreams)
	}
}
