package billing

import (
	"context"
	"encoding/json"

	"github.com/dsoplabs/dinbora-backend/internal/errors"
	"github.com/dsoplabs/dinbora-backend/internal/model/billing"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// HotmartWebhookData represents the structure of Hotmart webhook data
type HotmartWebhookData struct {
	Event       string `json:"event"`
	OrderID     string `json:"order_id"`
	OrderStatus string `json:"order_status"`
	Product     struct {
		ProductID   string `json:"product_id"`
		ProductName string `json:"product_name"`
	} `json:"Product"`
	Customer struct {
		FullName string `json:"full_name"`
		Email    string `json:"email"`
	} `json:"Customer"`
	Subscription struct {
		ID     string `json:"id"`
		Status string `json:"status"`
	} `json:"Subscription"`
	Amount   int    `json:"amount"`
	Currency string `json:"currency"`
}

// ApplePayWebhookData represents the structure of Apple Pay webhook data
type Apple<PERSON>ayWebhookData struct {
	NotificationType   string `json:"notificationType"`
	TransactionID      string `json:"transactionId"`
	OriginalTransactionID string `json:"originalTransactionId"`
	ProductID          string `json:"productId"`
	PurchaseDate       string `json:"purchaseDate"`
	ExpiresDate        string `json:"expiresDate"`
	Environment        string `json:"environment"`
	Status             string `json:"status"`
	Amount             int    `json:"amount"`
	Currency           string `json:"currency"`
}

// Hotmart webhook handlers
func (s *service) ProcessHotmartWebhook(ctx context.Context, webhookData interface{}) error {
	// Convert webhook data to HotmartWebhookData
	dataBytes, err := json.Marshal(webhookData)
	if err != nil {
		return errors.New(errors.Service, "failed to marshal hotmart webhook data", errors.Internal, err)
	}

	var hotmartData HotmartWebhookData
	if err := json.Unmarshal(dataBytes, &hotmartData); err != nil {
		return errors.New(errors.Service, "failed to unmarshal hotmart webhook data", errors.Validation, err)
	}

	// Route to appropriate handler based on event type
	switch hotmartData.Event {
	case "subscription.created", "order.completed":
		return s.HandleHotmartSubscriptionCreated(ctx, hotmartData)
	case "subscription.cancelled", "order.cancelled":
		return s.HandleHotmartSubscriptionCancelled(ctx, hotmartData)
	case "payment.completed", "order.paid":
		return s.HandleHotmartPaymentCompleted(ctx, hotmartData)
	case "payment.refunded", "order.refunded":
		return s.HandleHotmartPaymentRefunded(ctx, hotmartData)
	default:
		// Log unknown event but don't fail
		return nil
	}
}

func (s *service) HandleHotmartSubscriptionCreated(ctx context.Context, webhookData interface{}) error {
	hotmartData, ok := webhookData.(HotmartWebhookData)
	if !ok {
		return errors.New(errors.Service, "invalid hotmart webhook data", errors.Validation, nil)
	}

	// Find plan by Hotmart product ID
	plan, err := s.repo.Plans().FindByHotmartProductID(ctx, hotmartData.Product.ProductID)
	if err != nil {
		return errors.New(errors.Service, "plan not found for hotmart product", errors.NotFound, err)
	}

	// Find user by email (assuming user service integration)
	// For now, we'll skip user lookup and require userID in webhook
	// This would need integration with user service

	// Create subscription (this is a simplified version)
	// In real implementation, you'd need to get userID from email lookup
	userID := primitive.NewObjectID() // Placeholder

	subscription, err := s.CreateSubscription(ctx, userID, plan.ObjectID, billing.PaymentProviderHotmart)
	if err != nil {
		return err
	}

	// Set provider subscription ID
	subscription.ProviderSubscriptionID = hotmartData.Subscription.ID
	return s.repo.Subscriptions().Update(ctx, subscription)
}

func (s *service) HandleHotmartSubscriptionCancelled(ctx context.Context, webhookData interface{}) error {
	hotmartData, ok := webhookData.(HotmartWebhookData)
	if !ok {
		return errors.New(errors.Service, "invalid hotmart webhook data", errors.Validation, nil)
	}

	// Find subscription by provider subscription ID
	subscription, err := s.repo.Subscriptions().FindByProviderSubscriptionID(ctx, billing.PaymentProviderHotmart, hotmartData.Subscription.ID)
	if err != nil {
		return err
	}

	// Cancel subscription
	return s.CancelSubscription(ctx, subscription.ObjectID, "Cancelled via Hotmart webhook")
}

func (s *service) HandleHotmartPaymentCompleted(ctx context.Context, webhookData interface{}) error {
	hotmartData, ok := webhookData.(HotmartWebhookData)
	if !ok {
		return errors.New(errors.Service, "invalid hotmart webhook data", errors.Validation, nil)
	}

	// Find subscription by provider subscription ID
	var subscriptionID *primitive.ObjectID
	if hotmartData.Subscription.ID != "" {
		subscription, err := s.repo.Subscriptions().FindByProviderSubscriptionID(ctx, billing.PaymentProviderHotmart, hotmartData.Subscription.ID)
		if err == nil {
			subscriptionID = &subscription.ObjectID
		}
	}

	// Create or find payment
	userID := primitive.NewObjectID() // Placeholder - would need user lookup
	payment, err := s.ProcessPayment(ctx, userID, subscriptionID, billing.PaymentProviderHotmart, hotmartData.OrderID, int64(hotmartData.Amount), hotmartData.Currency, hotmartData)
	if err != nil {
		return err
	}

	// Mark payment as completed
	return s.MarkPaymentCompleted(ctx, payment.ObjectID)
}

func (s *service) HandleHotmartPaymentRefunded(ctx context.Context, webhookData interface{}) error {
	hotmartData, ok := webhookData.(HotmartWebhookData)
	if !ok {
		return errors.New(errors.Service, "invalid hotmart webhook data", errors.Validation, nil)
	}

	// Find payment by provider transaction ID
	payment, err := s.repo.Payments().FindByProviderTransactionID(ctx, billing.PaymentProviderHotmart, hotmartData.OrderID)
	if err != nil {
		return err
	}

	// Mark payment as refunded
	return s.MarkPaymentRefunded(ctx, payment.ObjectID)
}

// Apple Pay webhook handlers
func (s *service) ProcessApplePayWebhook(ctx context.Context, webhookData interface{}) error {
	// Convert webhook data to ApplePayWebhookData
	dataBytes, err := json.Marshal(webhookData)
	if err != nil {
		return errors.New(errors.Service, "failed to marshal apple pay webhook data", errors.Internal, err)
	}

	var appleData ApplePayWebhookData
	if err := json.Unmarshal(dataBytes, &appleData); err != nil {
		return errors.New(errors.Service, "failed to unmarshal apple pay webhook data", errors.Validation, err)
	}

	// Route to appropriate handler based on notification type
	switch appleData.NotificationType {
	case "INITIAL_BUY", "DID_RENEW":
		return s.HandleApplePaySubscriptionCreated(ctx, appleData)
	case "CANCEL", "DID_FAIL_TO_RENEW":
		return s.HandleApplePaySubscriptionCancelled(ctx, appleData)
	case "INTERACTIVE_RENEWAL":
		return s.HandleApplePayPaymentCompleted(ctx, appleData)
	case "REFUND":
		return s.HandleApplePayPaymentRefunded(ctx, appleData)
	default:
		// Log unknown notification type but don't fail
		return nil
	}
}

func (s *service) HandleApplePaySubscriptionCreated(ctx context.Context, webhookData interface{}) error {
	appleData, ok := webhookData.(ApplePayWebhookData)
	if !ok {
		return errors.New(errors.Service, "invalid apple pay webhook data", errors.Validation, nil)
	}

	// Find plan by Apple product ID
	plan, err := s.repo.Plans().FindByAppleProductID(ctx, appleData.ProductID)
	if err != nil {
		return errors.New(errors.Service, "plan not found for apple product", errors.NotFound, err)
	}

	// Create subscription (simplified version)
	userID := primitive.NewObjectID() // Placeholder

	subscription, err := s.CreateSubscription(ctx, userID, plan.ObjectID, billing.PaymentProviderApplePay)
	if err != nil {
		return err
	}

	// Set provider subscription ID
	subscription.ProviderSubscriptionID = appleData.OriginalTransactionID
	return s.repo.Subscriptions().Update(ctx, subscription)
}

func (s *service) HandleApplePaySubscriptionCancelled(ctx context.Context, webhookData interface{}) error {
	appleData, ok := webhookData.(ApplePayWebhookData)
	if !ok {
		return errors.New(errors.Service, "invalid apple pay webhook data", errors.Validation, nil)
	}

	// Find subscription by provider subscription ID
	subscription, err := s.repo.Subscriptions().FindByProviderSubscriptionID(ctx, billing.PaymentProviderApplePay, appleData.OriginalTransactionID)
	if err != nil {
		return err
	}

	// Cancel subscription
	return s.CancelSubscription(ctx, subscription.ObjectID, "Cancelled via Apple Pay webhook")
}

func (s *service) HandleApplePayPaymentCompleted(ctx context.Context, webhookData interface{}) error {
	appleData, ok := webhookData.(ApplePayWebhookData)
	if !ok {
		return errors.New(errors.Service, "invalid apple pay webhook data", errors.Validation, nil)
	}

	// Find subscription by provider subscription ID
	var subscriptionID *primitive.ObjectID
	if appleData.OriginalTransactionID != "" {
		subscription, err := s.repo.Subscriptions().FindByProviderSubscriptionID(ctx, billing.PaymentProviderApplePay, appleData.OriginalTransactionID)
		if err == nil {
			subscriptionID = &subscription.ObjectID
		}
	}

	// Create or find payment
	userID := primitive.NewObjectID() // Placeholder
	payment, err := s.ProcessPayment(ctx, userID, subscriptionID, billing.PaymentProviderApplePay, appleData.TransactionID, int64(appleData.Amount), appleData.Currency, appleData)
	if err != nil {
		return err
	}

	// Mark payment as completed
	return s.MarkPaymentCompleted(ctx, payment.ObjectID)
}

func (s *service) HandleApplePayPaymentRefunded(ctx context.Context, webhookData interface{}) error {
	appleData, ok := webhookData.(ApplePayWebhookData)
	if !ok {
		return errors.New(errors.Service, "invalid apple pay webhook data", errors.Validation, nil)
	}

	// Find payment by provider transaction ID
	payment, err := s.repo.Payments().FindByProviderTransactionID(ctx, billing.PaymentProviderApplePay, appleData.TransactionID)
	if err != nil {
		return err
	}

	// Mark payment as refunded
	return s.MarkPaymentRefunded(ctx, payment.ObjectID)
}
