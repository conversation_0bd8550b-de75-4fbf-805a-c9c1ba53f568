package user

import (
	"context"

	"github.com/dsoplabs/dinbora-backend/internal/model"
)

// FindEmployeesByHR retrieves all employees that were referred by the specified HR user
// This method uses the RBAC service to build appropriate filters and validate permissions
// It leverages the centralized RBAC logic for consistency with other role-based endpoints
func (s *service) FindEmployeesByHR(ctx context.Context, hrUserID string, hrUserRoles []string) ([]*model.User, error) {
	// Use RBAC service to build the appropriate filter for HR employee access
	// This handles role validation and creates the MongoDB filter for the referral relationship
	filter := s.RBACService.BuildHREmployeeFilter(ctx, hrUserID, hrUserRoles)

	// Query the repository using the RBAC-generated filter
	// This ensures consistent access control patterns across the application
	employees, err := s.Repository.FindWithFilter(ctx, filter)
	if err != nil {
		return nil, err
	}

	// Return the list of employees (could be empty if no employees found)
	return employees, nil
}
