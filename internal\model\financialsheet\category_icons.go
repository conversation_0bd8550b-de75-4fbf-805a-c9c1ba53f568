package financialsheet

type CategoryIcon string

const CategoryIconCDN = "https://images.dinbora.com.br/planilha-financeira"

// Undefined
const CategoryIconUndefined CategoryIcon = "undefined"

// Other
const CategoryIconOther CategoryIcon = CategoryIconCDN + "/outros.png"

// Compensation
const CategoryIconCompensation CategoryIcon = CategoryIconCDN + "/remuneracao"

const (
	CategoryIconCompensationCard      CategoryIcon = CategoryIconCompensation + "/remuneracao.png"
	CategoryIconCompensationFormal    CategoryIcon = CategoryIconCompensation + "/salario.png"
	CategoryIconCompensationContract  CategoryIcon = CategoryIconCompensation + "/contrato.png"
	CategoryIconCompensationProlabore CategoryIcon = CategoryIconCompensation + "/pro-labore.png"
	CategoryIconCompensationComission CategoryIcon = CategoryIconCompensation + "/comissao.png"
	CategoryIconCompensationBonus     CategoryIcon = CategoryIconCompensation + "/bonus.png"
	CategoryIconCompensationOthers    CategoryIcon = CategoryIconOther
)

// Benefits
const CategoryIconBenefits CategoryIcon = CategoryIconCDN + "/beneficios"

const (
	CategoryIconBenefitsCard                  CategoryIcon = CategoryIconBenefits + "/beneficios.png"
	CategoryIconBenefitsRetirement            CategoryIcon = CategoryIconBenefits + "/aposentadoria.png"
	CategoryIconBenefitsGovernment            CategoryIcon = CategoryIconBenefits + "/bolsa-familia.png"
	CategoryIconBenefitsSurvivorsPension      CategoryIcon = CategoryIconBenefits + "/pensao.png"
	CategoryIconBenefitsMealVoucher           CategoryIcon = CategoryIconBenefits + "/vale-refeicao.png"
	CategoryIconBenefitsTransportationVoucher CategoryIcon = CategoryIconBenefits + "/vale-transporte.png"
	CategoryIconBenefitsOthers                CategoryIcon = CategoryIconOther
)

// Investment Income
const CategoryIconInvestmentIncome CategoryIcon = CategoryIconCDN + "/renda-de-investimentos"

const (
	CategoryIconInvestmentIncomeCard         CategoryIcon = CategoryIconInvestmentIncome + "/renda-de-investimentos.png"
	CategoryIconInvestmentIncomeDividends    CategoryIcon = CategoryIconInvestmentIncome + "/dividendos.png"
	CategoryIconInvestmentIncomeFees         CategoryIcon = CategoryIconInvestmentIncome + "/juros.png"
	CategoryIconInvestmentIncomeRedemption   CategoryIcon = CategoryIconInvestmentIncome + "/resgate.png"
	CategoryIconInvestmentIncomeRentalIncome CategoryIcon = CategoryIconInvestmentIncome + "/renda.png"
	CategoryIconInvestmentIncomeRefund       CategoryIcon = CategoryIconInvestmentIncome + "/reembolso-de-imposto.png"
	CategoryIconInvestmentIncomeOthers       CategoryIcon = CategoryIconOther
)

// Additional Earnings
const CategoryIconAdditionalEarnings CategoryIcon = CategoryIconCDN + "/ganhos-adicionais"

const (
	CategoryIconAdditionalEarningsCard                    CategoryIcon = CategoryIconAdditionalEarnings + "/ganhos-adicionais.png"
	CategoryIconAdditionalEarningsSaleOfGoods             CategoryIcon = CategoryIconAdditionalEarnings + "/venda-de-bens.png"
	CategoryIconAdditionalEarningsSocialMediaMonetization CategoryIcon = CategoryIconAdditionalEarnings + "/redes-sociais.png"
	CategoryIconAdditionalEarningsCashback                CategoryIcon = CategoryIconAdditionalEarnings + "/cashback.png"
	CategoryIconAdditionalEarningsPrizes                  CategoryIcon = CategoryIconAdditionalEarnings + "/premio.png"
	CategoryIconAdditionalEarningsInheritance             CategoryIcon = CategoryIconAdditionalEarnings + "/heranca.png"
	CategoryIconAdditionalEarningsOthers                  CategoryIcon = CategoryIconOther
)

// Personal Needs
const CategoryIconPersonalNeeds CategoryIcon = CategoryIconCDN + "/necessidades"

const (
	CategoryIconPersonalNeedsCard           CategoryIcon = CategoryIconPersonalNeeds + "/necessidades.png"
	CategoryIconPersonalNeedsTithe          CategoryIcon = CategoryIconPersonalNeeds + "/dizimo.png"
	CategoryIconPersonalNeedsDonation       CategoryIcon = CategoryIconPersonalNeeds + "/doacao.png"
	CategoryIconPersonalNeedsClothes        CategoryIcon = CategoryIconPersonalNeeds + "/roupas.png"
	CategoryIconPersonalNeedsFurniture      CategoryIcon = CategoryIconPersonalNeeds + "/moveis.png"
	CategoryIconPersonalNeedsHomeAppliances CategoryIcon = CategoryIconPersonalNeeds + "/eletrodomestico.png"
	CategoryIconPersonalNeedsOthers         CategoryIcon = CategoryIconOther
)

// Dreams
const CategoryIconDreams CategoryIcon = CategoryIconCDN + "/sonhos"

const (
	CategoryIconDreamsCard       CategoryIcon = CategoryIconDreams + "/sonhos.png"
	CategoryIconShortTermDreams  CategoryIcon = CategoryIconDreams + "/curto-prazo.png"
	CategoryIconMediumTermDreams CategoryIcon = CategoryIconDreams + "/medio-prazo.png"
	CategoryIconLongTermDreams   CategoryIcon = CategoryIconDreams + "/longo-prazo.png"
	CategoryIconDreamsOthers     CategoryIcon = CategoryIconOther
)

// Debts
const CategoryIconDebts CategoryIcon = CategoryIconCDN + "/dividas"

const (
	CategoryIconDebtsCard         CategoryIcon = CategoryIconDebts + "/dividas.png"
	CategoryIconDebtsMortgage     CategoryIcon = CategoryIconDebts + "/financiamento-imobiliario.png"
	CategoryIconDebtsCarFinancing CategoryIcon = CategoryIconDebts + "/financiamento-veiculo.png"
	CategoryIconDebtsCreditCard   CategoryIcon = CategoryIconDebts + "/cartao-de-credito.png"
	CategoryIconDebtsOverdraft    CategoryIcon = CategoryIconDebts + "/cheque-especial.png"
	CategoryIconDebtsPersonalLoan CategoryIcon = CategoryIconDebts + "/credito-consignado.png"
	CategoryIconDebtsOthers       CategoryIcon = CategoryIconOther
)

// Personal Reserves
const CategoryIconPersonalReserves CategoryIcon = CategoryIconCDN + "/reserva"

const (
	CategoryIconPersonalReservesCard             CategoryIcon = CategoryIconPersonalReserves + "/reserva.png"
	CategoryIconPersonalReservesAssetAcquisition CategoryIcon = CategoryIconPersonalReserves + "/aquisicao-patrimonial.png"
	CategoryIconPersonalReservesRetirement       CategoryIcon = CategoryIconPersonalReserves + "/previdencia-privada.png"
	CategoryIconPersonalReservesStrategic        CategoryIcon = CategoryIconPersonalReserves + "/reserva-estrategica.png"
	CategoryIconPersonalReservesInvestments      CategoryIcon = CategoryIconPersonalReserves + "/investimento.png"
	CategoryIconPersonalReservesLifeInsurance    CategoryIcon = CategoryIconPersonalReserves + "/seguro-de-vida.png"
	CategoryIconPersonalReservesOthers           CategoryIcon = CategoryIconOther
)

// Housing
const CategoryIconHousing CategoryIcon = CategoryIconCDN + "/moradia"

const (
	CategoryIconHousingCard         CategoryIcon = CategoryIconHousing + "/moradia.png"
	CategoryIconHousingRentMortgage CategoryIcon = CategoryIconHousing + "/aluguel.png"
	CategoryIconHousingCondoFees    CategoryIcon = CategoryIconHousing + "/condominio.png"
	CategoryIconHousingMaintenance  CategoryIcon = CategoryIconHousing + "/manutencao.png"
	CategoryIconHousingInsurance    CategoryIcon = CategoryIconHousing + "/seguro-residencial.png"
	CategoryIconHousingOthers       CategoryIcon = CategoryIconOther
)

// House Bills
const CategoryIconHouseBills CategoryIcon = CategoryIconCDN + "/contas-de-casa"

const (
	CategoryIconHouseBillsCard           CategoryIcon = CategoryIconHouseBills + "/contas-de-casa.png"
	CategoryIconHouseBillsWater          CategoryIcon = CategoryIconHouseBills + "/conta-de-agua.png"
	CategoryIconHouseBillsElectricity    CategoryIcon = CategoryIconHouseBills + "/conta-de-luz.png"
	CategoryIconHouseBillsGas            CategoryIcon = CategoryIconHouseBills + "/conta-de-gas.png"
	CategoryIconHouseBillsCollectionFees CategoryIcon = CategoryIconHouseBills + "/taxa-de-coleta.png"
	CategoryIconHouseBillsOthers         CategoryIcon = CategoryIconOther
)

// Education
const CategoryIconEducation CategoryIcon = CategoryIconCDN + "/educacao"

const (
	CategoryIconEducationCard           CategoryIcon = CategoryIconEducation + "/educacao.png"
	CategoryIconEducationFormal         CategoryIcon = CategoryIconEducation + "/ensino-formal.png"
	CategoryIconEducationFreeCourses    CategoryIcon = CategoryIconEducation + "/cursos-livres.png"
	CategoryIconEducationLanguages      CategoryIcon = CategoryIconEducation + "/idiomas.png"
	CategoryIconEducationPlatforms      CategoryIcon = CategoryIconEducation + "/plataformas-educacionais.png"
	CategoryIconEducationBooksMaterials CategoryIcon = CategoryIconEducation + "/livros-e-materiais.png"
	CategoryIconEducationOthers         CategoryIcon = CategoryIconOther
)

// Family
const CategoryIconFamily CategoryIcon = CategoryIconCDN + "/familia"

const (
	CategoryIconFamilyCard       CategoryIcon = CategoryIconFamily + "/familia.png"
	CategoryIconFamilyAllowance  CategoryIcon = CategoryIconFamily + "/mesada.png"
	CategoryIconFamilyPets       CategoryIcon = CategoryIconFamily + "/pets.png"
	CategoryIconFamilyGifts      CategoryIcon = CategoryIconFamily + "/presente.png"
	CategoryIconFamilyAssistance CategoryIcon = CategoryIconFamily + "/assistencia-familiares.png"
	CategoryIconFamilyEvents     CategoryIcon = CategoryIconFamily + "/eventos-familiares.png"
	CategoryIconFamilyOthers     CategoryIcon = CategoryIconOther
)

// Communication
const CategoryIconCommunication CategoryIcon = CategoryIconCDN + "/comunicacao"

const (
	CategoryIconCommunicationCard                 CategoryIcon = CategoryIconCommunication + "/comunicacao.png"
	CategoryIconCommunicationInternet             CategoryIcon = CategoryIconCommunication + "/internet.png"
	CategoryIconCommunicationMobilePhone          CategoryIcon = CategoryIconCommunication + "/conta-celular.png"
	CategoryIconCommunicationCableTV              CategoryIcon = CategoryIconCommunication + "/tv-por-assinatura.png"
	CategoryIconCommunicationStreaming            CategoryIcon = CategoryIconCommunication + "/aplicativo-de-streaming.png"
	CategoryIconCommunicationDigitalSubscriptions CategoryIcon = CategoryIconCommunication + "/assinaturas-digitais.png"
	CategoryIconCommunicationOthers               CategoryIcon = CategoryIconOther
)

// Transportation
const CategoryIconTransportation CategoryIcon = CategoryIconCDN + "/transporte"

const (
	CategoryIconTransportationCard           CategoryIcon = CategoryIconTransportation + "/transporte.png"
	CategoryIconTransportationTickets        CategoryIcon = CategoryIconTransportation + "/passagem.png"
	CategoryIconTransportationMobilityApps   CategoryIcon = CategoryIconTransportation + "/app-de-mobilidade.png"
	CategoryIconTransportationFuel           CategoryIcon = CategoryIconTransportation + "/combustivel.png"
	CategoryIconTransportationCarInsurance   CategoryIcon = CategoryIconTransportation + "/seguro-de-automovel.png"
	CategoryIconTransportationCarMaintenance CategoryIcon = CategoryIconTransportation + "/manutencao.png"
	CategoryIconTransportationOthers         CategoryIcon = CategoryIconOther
)

// Health
const CategoryIconHealth CategoryIcon = CategoryIconCDN + "/saude"

const (
	CategoryIconHealthCard           CategoryIcon = CategoryIconHealth + "/saude.png"
	CategoryIconHealthHealthcarePlan CategoryIcon = CategoryIconHealth + "/convenio-de-saude.png"
	CategoryIconHealthMedications    CategoryIcon = CategoryIconHealth + "/medicamentos.png"
	CategoryIconHealthDentist        CategoryIcon = CategoryIconHealth + "/dentista.png"
	CategoryIconHealthDoctor         CategoryIcon = CategoryIconHealth + "/medico.png"
	CategoryIconHealthMedicalTests   CategoryIcon = CategoryIconHealth + "/exames.png"
	CategoryIconHealthOthers         CategoryIcon = CategoryIconOther
)

// Personal Care
const CategoryIconPersonalCare CategoryIcon = CategoryIconCDN + "/cuidado-pessoal"

const (
	CategoryIconPersonalCareCard        CategoryIcon = CategoryIconPersonalCare + "/cuidado-pessoal.png"
	CategoryIconPersonalCareSalonBarber CategoryIcon = CategoryIconPersonalCare + "/salao-barbearia.png"
	CategoryIconPersonalCareGym         CategoryIcon = CategoryIconPersonalCare + "/academia.png"
	CategoryIconPersonalCareSupplements CategoryIcon = CategoryIconPersonalCare + "/suplementos.png"
	CategoryIconPersonalCareTherapy     CategoryIcon = CategoryIconPersonalCare + "/terapia.png"
	CategoryIconPersonalCareHygiene     CategoryIcon = CategoryIconPersonalCare + "/higiene-pessoal.png"
	CategoryIconPersonalCareOthers      CategoryIcon = CategoryIconOther
)

// Food
const CategoryIconFood CategoryIcon = CategoryIconCDN + "/alimentacao"

const (
	CategoryIconFoodCard          CategoryIcon = CategoryIconFood + "/alimentacao.png"
	CategoryIconFoodSupermarket   CategoryIcon = CategoryIconFood + "/supermercado.png"
	CategoryIconFoodFarmersMarket CategoryIcon = CategoryIconFood + "/feira.png"
	CategoryIconFoodButcherShop   CategoryIcon = CategoryIconFood + "/acougue.png"
	CategoryIconFoodBakery        CategoryIcon = CategoryIconFood + "/padaria.png"
	CategoryIconFoodDelivery      CategoryIcon = CategoryIconFood + "/delivery.png"
	CategoryIconFoodOthers        CategoryIcon = CategoryIconOther
)

// Leisure
const CategoryIconLeisure CategoryIcon = CategoryIconCDN + "/lazer"

const (
	CategoryIconLeisureCard            CategoryIcon = CategoryIconLeisure + "/lazer.png"
	CategoryIconLeisureTravel          CategoryIcon = CategoryIconLeisure + "/viagem.png"
	CategoryIconLeisureEvents          CategoryIcon = CategoryIconLeisure + "/eventos.png"
	CategoryIconLeisureCinema          CategoryIcon = CategoryIconLeisure + "/cinema.png"
	CategoryIconLeisureParties         CategoryIcon = CategoryIconLeisure + "/festas.png"
	CategoryIconLeisureBarsRestaurants CategoryIcon = CategoryIconLeisure + "/bar.png"
	CategoryIconLeisureOthers          CategoryIcon = CategoryIconOther
)

// Casual Shopping
const CategoryIconCasualShopping CategoryIcon = CategoryIconCDN + "/compras-casuais"

const (
	CategoryIconCasualShoppingCard             CategoryIcon = CategoryIconCasualShopping + "/compras-casuais.png"
	CategoryIconCasualShoppingClothingFootwear CategoryIcon = CategoryIconCasualShopping + "/roupas.png"
	CategoryIconCasualShoppingAccessories      CategoryIcon = CategoryIconCasualShopping + "/acessorios.png"
	CategoryIconCasualShoppingElectronics      CategoryIcon = CategoryIconCasualShopping + "/eletronicos.png"
	CategoryIconCasualShoppingCosmetics        CategoryIcon = CategoryIconCasualShopping + "/cosmeticos.png"
	CategoryIconCasualShoppingLaundry          CategoryIcon = CategoryIconCasualShopping + "/lavanderia.png"
	CategoryIconCasualShoppingOthers           CategoryIcon = CategoryIconOther
)

// New Category
const CategoryIconNewCategory CategoryIcon = CategoryIconCDN + "/novas"
const CategoryIconNewCategoryIncome CategoryIcon = CategoryIconNewCategory + "/ganhos.png"
const CategoryIconNewCategoryExpense CategoryIcon = CategoryIconNewCategory + "/gastos.png"
const CategoryIconNewCategoryOthers CategoryIcon = CategoryIconOther

// Income
const CategoryIconIncome CategoryIcon = CategoryIconCDN + "/entrada"
const (
	CategoryIconIncomeBankTransfer CategoryIcon = CategoryIconIncome + "/transferencia-bancaria.png"
	CategoryIconIncomeCash         CategoryIcon = CategoryIconIncome + "/dinheiro.png"
	CategoryIconIncomePix          CategoryIcon = CategoryIconIncome + "/pix.png"
	CategoryIconIncomeCreditCard   CategoryIcon = CategoryIconIncome + "/cartao-de-credito.png"
	CategoryIconIncomeCheck        CategoryIcon = CategoryIconIncome + "/cheque.png"
	CategoryIconIncomeOthers       CategoryIcon = CategoryIconOther
)

// Expense
const CategoryIconExpense CategoryIcon = CategoryIconCDN + "/saida"
const (
	CategoryIconExpenseCash       CategoryIcon = CategoryIconExpense + "/dinheiro.png"
	CategoryIconExpensePix        CategoryIcon = CategoryIconExpense + "/pix.png"
	CategoryIconExpenseDebitCard  CategoryIcon = CategoryIconExpense + "/cartao-de-debito.png"
	CategoryIconExpenseCreditCard CategoryIcon = CategoryIconExpense + "/cartao-de-credito.png"
	CategoryIconExpenseBankSlip   CategoryIcon = CategoryIconExpense + "/boleto.png"
	CategoryIconExpenseOthers     CategoryIcon = CategoryIconOther
)
