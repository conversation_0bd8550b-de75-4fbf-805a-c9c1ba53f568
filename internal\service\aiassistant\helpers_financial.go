package aiassistant

import (
	"context"
	"sort"
	"time"

	"github.com/dsoplabs/dinbora-backend/internal/model/aiassistant"
	"github.com/dsoplabs/dinbora-backend/internal/model/financialsheet"
)

// fetchFinancialHabitsData retrieves and formats financial habits data
func (s *service) fetchFinancialHabitsData(ctx context.Context, userID string) (aiassistant.FinancialHabitsSummaryDTO, error) {
	// Get current year and month
	now := time.Now()
	currentYear := now.Year()
	currentMonthInt := int(now.Month())
	currentMonthStr := now.Format("01") // Format month as string (01-12)

	// Get financial sheet data for current month
	financialRecord, err := s.FinancialSheetService.FindByUserAndPeriod(ctx, userID, currentYear, currentMonthInt, true)
	if err != nil {
		return aiassistant.FinancialHabitsSummaryDTO{}, err
	}

	// Initialize the DTO
	habitsDTO := aiassistant.FinancialHabitsSummaryDTO{
		MonthlyIncome:        int64(financialRecord.TotalIncome),
		MonthlyCostsOfLiving: int64(financialRecord.TotalCostsOfLiving),
		MonthlyExpenses:      int64(financialRecord.TotalExpenses),
		MonthlyBalance:       int64(financialRecord.Balance),
	}

	// Calculate savings rate
	if financialRecord.TotalIncome > 0 {
		habitsDTO.SavingsRate = float64(financialRecord.Balance) / float64(financialRecord.TotalIncome) * 100
	}

	// Process income categories
	incomeCategories := make([]aiassistant.CategorySummaryDTO, 0)
	for _, category := range financialRecord.Categories {
		if category.Type == financialsheet.CategoryTypeIncome {
			categoryDTO := aiassistant.CategorySummaryDTO{
				Name:   category.Name,
				Amount: int64(category.Value),
			}

			// Calculate percentage of total income
			if financialRecord.TotalIncome > 0 {
				categoryDTO.Percent = float64(category.Value) / float64(financialRecord.TotalIncome) * 100
			}

			incomeCategories = append(incomeCategories, categoryDTO)
		}
	}

	// Sort income categories by amount (descending)
	sort.Slice(incomeCategories, func(i, j int) bool {
		return incomeCategories[i].Amount > incomeCategories[j].Amount
	})

	// Take top 5 income categories
	if len(incomeCategories) > 5 {
		habitsDTO.TopIncomeCategories = incomeCategories[:5]
	} else {
		habitsDTO.TopIncomeCategories = incomeCategories
	}

	// Process expense categories (including costs of living)
	expenseCategories := make([]aiassistant.CategorySummaryDTO, 0)
	for _, category := range financialRecord.Categories {
		if category.Type == financialsheet.CategoryTypeExpense || category.Type == financialsheet.CategoryTypeCostsOfLiving {
			categoryDTO := aiassistant.CategorySummaryDTO{
				Name:   category.Name,
				Amount: int64(category.Value),
			}

			// Calculate percentage of total expenses
			totalExpenses := int64(financialRecord.TotalExpenses + financialRecord.TotalCostsOfLiving)
			if totalExpenses > 0 {
				categoryDTO.Percent = float64(category.Value) / float64(totalExpenses) * 100
			}

			expenseCategories = append(expenseCategories, categoryDTO)
		}
	}

	// Sort expense categories by amount (descending)
	sort.Slice(expenseCategories, func(i, j int) bool {
		return expenseCategories[i].Amount > expenseCategories[j].Amount
	})

	// Take top 5 expense categories
	if len(expenseCategories) > 5 {
		habitsDTO.TopExpenseCategories = expenseCategories[:5]
	} else {
		habitsDTO.TopExpenseCategories = expenseCategories
	}

	// Process recent transactions
	recentTransactions := make([]aiassistant.TransactionSummaryDTO, 0)

	// Get transactions from current month
	if yearData, ok := financialRecord.YearData[currentYear]; ok {
		if monthData, ok := yearData[currentMonthStr]; ok {
			// Process all transactions
			for _, transaction := range monthData.Transactions {
				// Find category name
				categoryName := "Outros"
				for _, category := range financialRecord.Categories {
					if category.Identifier == string(transaction.Category) {
						categoryName = category.Name
						break
					}
				}

				// Determine transaction type
				transactionType := "expense"
				for _, category := range financialRecord.Categories {
					if category.Identifier == string(transaction.Category) {
						switch category.Type {
						case financialsheet.CategoryTypeIncome:
							transactionType = "income"
						case financialsheet.CategoryTypeCostsOfLiving:
							transactionType = "cost_of_living"
						}
						break
					}
				}

				// Create transaction DTO
				transactionDTO := aiassistant.TransactionSummaryDTO{
					Category:        categoryName,
					Amount:          int64(transaction.Value),
					Date:            transaction.Date.Format("2006-01-02"),
					Type:            transactionType,
					AttachedDreamID: transaction.AttachedDreamID,
				}

				recentTransactions = append(recentTransactions, transactionDTO)
			}
		}
	}

	// Sort transactions by date (most recent first)
	sort.Slice(recentTransactions, func(i, j int) bool {
		dateI, _ := time.Parse("2006-01-02", recentTransactions[i].Date)
		dateJ, _ := time.Parse("2006-01-02", recentTransactions[j].Date)
		return dateI.After(dateJ)
	})

	// Take most recent 10 transactions
	if len(recentTransactions) > 10 {
		habitsDTO.RecentTransactions = recentTransactions[:10]
	} else {
		habitsDTO.RecentTransactions = recentTransactions
	}

	return habitsDTO, nil
}

// fetchSimulationHighlightsData retrieves and formats simulation highlights data
func (s *service) fetchSimulationHighlightsData(ctx context.Context, userID string) ([]aiassistant.SimulationHighlightDTO, error) {
	// This is a placeholder for future implementation
	// Currently returning an empty slice as this feature may not be fully implemented yet
	return []aiassistant.SimulationHighlightDTO{}, nil
}
