package google

import (
	"context"
	"crypto/rand"
	"encoding/base64"
	"encoding/json"
	"io"
	"net/http"
	"net/url"
	"os"

	"github.com/dsoplabs/dinbora-backend/internal/api/token"
	"github.com/dsoplabs/dinbora-backend/internal/errors"
	"github.com/dsoplabs/dinbora-backend/internal/model"
	"github.com/dsoplabs/dinbora-backend/internal/service/user"
	"golang.org/x/oauth2"
	"golang.org/x/oauth2/google"
)

type Service interface {
	// Core Auth
	Register(googleUserDetails GoogleUserDetails, onboarding *model.Onboarding, referralCode string) (*token.Token, error)
	Login(googleUserDetails GoogleUserDetails) (*token.Token, error)

	// Utility
	CallBackFromGoogle(code string) (*oauth2.Token, error)
	GetUserInfoFromGoogle(token string) (GoogleUserDetails, error)
	OauthState() string
	RegisterInformation() *GoogleRegisterInformation
	SetupOAuth2() (string, error)
}

// GoogleRegisterInformation
type GoogleRegisterInformation struct {
	Access     string
	Onboarding *model.Onboarding
}

// GoogleUserDetails
type GoogleUserDetails struct {
	ID             string
	Email          string
	Verified_Email bool
	Name           string
	Given_Name     string
	Family_Name    string
	Picture        string
	Locale         string
}

type service struct {
	Config                    *oauth2.Config
	OauthStateGoogle          string
	UserService               user.Service
	GoogleRegisterInformation GoogleRegisterInformation
}

func New(userService user.Service) Service {
	return &service{
		Config: &oauth2.Config{
			ClientID:     os.Getenv("GOOGLE_CLIENT_ID"),
			ClientSecret: os.Getenv("GOOGLE_CLIENT_SECRET"),
			RedirectURL:  os.Getenv("GOOGLE_REDIRECT_URL"),
			Scopes:       []string{"https://www.googleapis.com/auth/userinfo.email", "https://www.googleapis.com/auth/userinfo.profile"},
			Endpoint:     google.Endpoint,
		},
		OauthStateGoogle: "",
		UserService:      userService,
	}
}

// Core Auth
// Register
func (s *service) Register(googleUserDetails GoogleUserDetails, onboarding *model.Onboarding, referralCode string) (*token.Token, error) {
	user := &model.User{
		Name:       googleUserDetails.Name,
		Email:      googleUserDetails.Email,
		Password:   s.randomOAuthStateString(10) + "@" + s.randomOAuthStateString(10),
		PhotoURL:   googleUserDetails.Picture,
		Onboarding: onboarding,
	}

	if err := user.PrepareCreate(); err != nil {
		return nil, err
	}

	if err := s.UserService.Create(context.TODO(), user, referralCode); err != nil {
		return nil, err
	}

	createdUser, err := s.UserService.FindByEmail(context.TODO(), user.Email)
	if err != nil {
		return nil, err
	}

	token, err := token.Create(createdUser)
	if err != nil {
		return nil, err
	}

	return token, nil
}

func (s *service) Login(googleUserDetails GoogleUserDetails) (*token.Token, error) {
	user, err := s.UserService.FindByEmail(context.TODO(), googleUserDetails.Email)
	if err != nil {
		return nil, err
	}

	// Log the user in.
	err = user.PrepareLogin()
	if err != nil {
		return nil, err
	}

	token, err := token.Create(user)
	if err != nil {
		return nil, err
	}

	return token, nil
}

// Utility
// CallBackFromGoogle
func (s *service) CallBackFromGoogle(code string) (*oauth2.Token, error) {

	token, err := s.Config.Exchange(oauth2.NoContext, code)
	if err != nil {
		return nil, err
	}

	return token, nil
}

// GetUserInfoFromGoogle
func (s *service) GetUserInfoFromGoogle(token string) (GoogleUserDetails, error) {
	var googleUserDetails GoogleUserDetails

	googleUserDetailsRequest, _ := http.NewRequest("GET", "https://www.googleapis.com/oauth2/v2/userinfo?access_token="+url.QueryEscape(token), nil)
	googleUserDetailsResponse, googleUserDetailsResponseError := http.DefaultClient.Do(googleUserDetailsRequest)

	if googleUserDetailsResponseError != nil {
		return GoogleUserDetails{}, errors.OldError("fail to fetch google user details", errors.Internal, nil)
	}

	// If you want show user information
	// response, _ := ioutil.ReadAll(googleUserDetailsResponse.Body)
	// log.Println((string(response)))

	decoder := json.NewDecoder(googleUserDetailsResponse.Body)
	decoderErr := decoder.Decode(&googleUserDetails)
	defer googleUserDetailsResponse.Body.Close()

	if decoderErr != nil {
		return GoogleUserDetails{}, errors.OldError("decode error google json", errors.Validation, nil)
	}

	return googleUserDetails, nil
}

// RegisterInformation will return the google register information.
func (s *service) RegisterInformation() *GoogleRegisterInformation {
	return &s.GoogleRegisterInformation
}

// OauthState will return the oauth state for google.
func (s *service) OauthState() string {
	return s.OauthStateGoogle
}

// SetupOAuth2 will return the google oauth url.
func (s *service) SetupOAuth2() (string, error) {
	s.OauthStateGoogle = s.randomOAuthStateString(20)
	if s.OauthStateGoogle == "" {
		return "", errors.New(errors.Service, "empty oauth state", errors.Validation, nil)
	}

	url := s.Config.AuthCodeURL(s.OauthStateGoogle)
	if url == "" {
		return "", errors.New(errors.Service, "empty facebook oauth url", errors.Unauthorized, nil)
	}
	return url, nil
}

// Helper
// randomOAuthStateString will return a random string to increase security.
func (s *service) randomOAuthStateString(n int) string {
	data := make([]byte, n)
	if _, err := io.ReadFull(rand.Reader, data); err != nil {
		return ""
	}

	return base64.StdEncoding.EncodeToString(data)
}
