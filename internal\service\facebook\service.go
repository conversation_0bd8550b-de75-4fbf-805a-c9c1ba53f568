package facebook

import (
	"context"
	"crypto/rand"
	"encoding/base64"
	"encoding/json"
	"io"
	"net/http"
	"net/url"
	"os"

	"github.com/dsoplabs/dinbora-backend/internal/api/token"
	"github.com/dsoplabs/dinbora-backend/internal/errors"
	"github.com/dsoplabs/dinbora-backend/internal/model"
	"github.com/dsoplabs/dinbora-backend/internal/service/user"
	"golang.org/x/oauth2"
	_facebook "golang.org/x/oauth2/facebook"
)

// Service
type Service interface {
	// Core Auth
	Register(facebookUserDetails FacebookUserDetails, onboarding *model.Onboarding, referralCode string) (*token.Token, error)
	Login(facebookUserDetails FacebookUserDetails) (*token.Token, error)

	// Utility
	CallBackFromFacebook(code string) (*oauth2.Token, error)
	GetUserInfoFromFacebook(token string) (FacebookUserDetails, error)
	OauthState() string
	RegisterInformation() *FacebookRegisterInformation
	SetupOAuth2() (string, error)
}

// FacebookRegisterInformation
type FacebookRegisterInformation struct {
	Access     string
	Onboarding *model.Onboarding
}

// FacebookUserDetails
type FacebookUserDetails struct {
	ID          string
	Email       string
	Name        string
	First_Name  string
	Middle_Name string
	Last_Name   string
	Picture     map[string]interface{}
}

type service struct {
	Config                      *oauth2.Config
	OauthStateFacebook          string
	UserService                 user.Service
	FacebookRegisterInformation FacebookRegisterInformation
}

func New(userService user.Service) Service {
	return &service{
		Config: &oauth2.Config{
			ClientID:     os.Getenv("FACEBOOK_CLIENT_ID"),
			ClientSecret: os.Getenv("FACEBOOK_CLIENT_SECRET"),
			RedirectURL:  os.Getenv("FACEBOOK_REDIRECT_URL"),
			Scopes:       []string{"email"},
			Endpoint:     _facebook.Endpoint,
		},
		OauthStateFacebook: "",
		UserService:        userService,
	}
}

// Core Auth
// Register
func (s *service) Register(facebookUserDetails FacebookUserDetails, onboarding *model.Onboarding, referralCode string) (*token.Token, error) {
	user := &model.User{
		Name:       facebookUserDetails.Name,
		Email:      facebookUserDetails.Email,
		Password:   s.randomOAuthStateString(10) + "@" + s.randomOAuthStateString(10),
		PhotoURL:   facebookUserDetails.Picture["data"].(map[string]interface{})["url"].(string),
		Onboarding: onboarding,
	}

	if err := user.PrepareCreate(); err != nil {
		return nil, err
	}

	if err := s.UserService.Create(context.TODO(), user, referralCode); err != nil {
		return nil, err
	}

	createdUser, err := s.UserService.FindByEmail(context.TODO(), user.Email)
	if err != nil {
		return nil, err
	}

	token, err := token.Create(createdUser)
	if err != nil {
		return nil, err
	}

	return token, nil
}

// Login
func (s *service) Login(facebookUserDetails FacebookUserDetails) (*token.Token, error) {
	user, err := s.UserService.FindByEmail(context.TODO(), facebookUserDetails.Email)
	if err != nil {
		return nil, err
	}

	if err := user.PrepareLogin(); err != nil {
		return nil, err
	}

	token, err := token.Create(user)
	if err != nil {
		return nil, err
	}

	return token, nil
}

// Utility
// CallBackFromFacebook
func (s *service) CallBackFromFacebook(code string) (*oauth2.Token, error) {
	token, err := s.Config.Exchange(oauth2.NoContext, code)
	if err != nil {
		return nil, err
	}

	// log.Println("TOKEN>> AccessToken>> " + token.AccessToken)
	// log.Println("TOKEN>> Expiration Time>> " + token.Expiry.String())
	// log.Println("TOKEN>> RefreshToken>> " + token.RefreshToken)

	return token, nil
}

// GetUserInfoFromFacebook
func (s *service) GetUserInfoFromFacebook(token string) (FacebookUserDetails, error) {
	var facebookUserDetails FacebookUserDetails
	facebookUserDetailsRequest, _ := http.NewRequest("GET", "https://graph.facebook.com/me?fields=id,email,name,first_name,middle_name,last_name,picture&access_token="+url.QueryEscape(token), nil)
	facebookUserDetailsResponse, facebookUserDetailsResponseError := http.DefaultClient.Do(facebookUserDetailsRequest)

	if facebookUserDetailsResponseError != nil {
		return FacebookUserDetails{}, errors.OldError("fail to fetch facebook user details", errors.Internal, nil)
	}

	// // If you want show user information
	// bodyBytes, err := io.ReadAll(facebookUserDetailsResponse.Body)
	// if err != nil {
	// 	log.Fatal(err)
	// }
	// bodyString := string(bodyBytes)
	// log.Println(bodyString)

	decoder := json.NewDecoder(facebookUserDetailsResponse.Body)
	decoderErr := decoder.Decode(&facebookUserDetails)
	defer facebookUserDetailsResponse.Body.Close()

	if decoderErr != nil {
		return FacebookUserDetails{}, errors.OldError("decode error facebook json", errors.Validation, nil)
	}

	return facebookUserDetails, nil
}

// OauthState will return the oauth state for facebook.
func (s *service) OauthState() string {
	return s.OauthStateFacebook
}

// RegisterInformation will return the facebook register information.
func (s *service) RegisterInformation() *FacebookRegisterInformation {
	return &s.FacebookRegisterInformation
}

// SetupOAuth2 will return the facebook oauth url.
func (s *service) SetupOAuth2() (string, error) {
	s.OauthStateFacebook = s.randomOAuthStateString(20)
	if s.OauthStateFacebook == "" {
		return "", errors.New(errors.Service, "empty oauth state", errors.Validation, nil)
	}

	url := s.Config.AuthCodeURL(s.OauthStateFacebook)
	if url == "" {
		return "", errors.New(errors.Service, "empty facebook oauth url", errors.Unauthorized, nil)
	}

	return url, nil
}

// Helper
// randomOAuthStateString will return a random string to increase security.
func (s *service) randomOAuthStateString(n int) string {
	data := make([]byte, n)
	if _, err := io.ReadFull(rand.Reader, data); err != nil {
		return ""
	}

	return base64.StdEncoding.EncodeToString(data)
}
