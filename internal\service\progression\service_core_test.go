package progression

import (
	"context"
	"testing"
	"time"

	"github.com/dsoplabs/dinbora-backend/internal/errors"
	"github.com/dsoplabs/dinbora-backend/internal/model"
	"github.com/dsoplabs/dinbora-backend/internal/model/content"
	"github.com/dsoplabs/dinbora-backend/internal/model/progression"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// Mock repositories
type MockProgressionRepo struct {
	mock.Mock
}

func (m *MockProgressionRepo) Create(ctx context.Context, progression *progression.Progression) error {
	args := m.Called(ctx, progression)
	return args.Error(0)
}

func (m *MockProgressionRepo) Find(ctx context.Context, id string) (*progression.Progression, error) {
	args := m.Called(ctx, id)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*progression.Progression), args.Error(1)
}

func (m *MockProgressionRepo) FindByUser(ctx context.Context, userId string) (*progression.Progression, error) {
	args := m.Called(ctx, userId)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*progression.Progression), args.Error(1)
}

func (m *MockProgressionRepo) FindAllTrophies(ctx context.Context, userId string) ([]*progression.Trophy, error) {
	args := m.Called(ctx, userId)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).([]*progression.Trophy), args.Error(1)
}

func (m *MockProgressionRepo) FindForCards(ctx context.Context, userId string) (map[string]*progression.Trail, error) {
	args := m.Called(ctx, userId)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(map[string]*progression.Trail), args.Error(1)
}

func (m *MockProgressionRepo) FindLessonProgressionsForCards(ctx context.Context, userId string, trailId string) (map[string]*progression.Lesson, error) {
	args := m.Called(ctx, userId, trailId)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(map[string]*progression.Lesson), args.Error(1)
}

func (m *MockProgressionRepo) FindChallengeProgressionForCards(ctx context.Context, userId string, trailId string) (*progression.Challenge, error) {
	args := m.Called(ctx, userId, trailId)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*progression.Challenge), args.Error(1)
}

func (m *MockProgressionRepo) Update(ctx context.Context, progression *progression.Progression) error {
	args := m.Called(ctx, progression)
	return args.Error(0)
}

func (m *MockProgressionRepo) Delete(ctx context.Context, id string) error {
	args := m.Called(ctx, id)
	return args.Error(0)
}

// Mock trail service
type MockTrailSvc struct {
	mock.Mock
}

func (m *MockTrailSvc) Find(ctx context.Context, id string) (*content.Trail, error) {
	args := m.Called(ctx, id)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*content.Trail), args.Error(1)
}

func (m *MockTrailSvc) FindAll(ctx context.Context) ([]*content.Trail, error) {
	args := m.Called(ctx)
	return args.Get(0).([]*content.Trail), args.Error(1)
}

func (m *MockTrailSvc) FindByIdentifier(ctx context.Context, identifier string) (*content.Trail, error) {
	args := m.Called(ctx, identifier)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*content.Trail), args.Error(1)
}

func (m *MockTrailSvc) FindCardData(ctx context.Context, id string) (*content.TrailCard, error) {
	args := m.Called(ctx, id)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*content.TrailCard), args.Error(1)
}

func (m *MockTrailSvc) FindAllCardData(ctx context.Context) ([]*content.TrailCard, error) {
	args := m.Called(ctx)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).([]*content.TrailCard), args.Error(1)
}

func (m *MockTrailSvc) CreateRegularTrail(ctx context.Context, trail *content.Trail) error {
	args := m.Called(ctx, trail)
	return args.Error(0)
}

func (m *MockTrailSvc) FindRegularTrail(ctx context.Context, id string) (*content.Trail, error) {
	args := m.Called(ctx, id)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*content.Trail), args.Error(1)
}

func (m *MockTrailSvc) FindAllRegularTrails(ctx context.Context) ([]*content.Trail, error) {
	args := m.Called(ctx)
	return args.Get(0).([]*content.Trail), args.Error(1)
}

func (m *MockTrailSvc) FindRegularTrailByIdentifier(ctx context.Context, identifier string) (*content.Trail, error) {
	args := m.Called(ctx, identifier)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*content.Trail), args.Error(1)
}

func (m *MockTrailSvc) UpdateRegularTrail(ctx context.Context, trail *content.Trail) error {
	args := m.Called(ctx, trail)
	return args.Error(0)
}

func (m *MockTrailSvc) DeleteRegularTrail(ctx context.Context, id string) error {
	args := m.Called(ctx, id)
	return args.Error(0)
}

func (m *MockTrailSvc) FindRegularTrailCardData(ctx context.Context, id string) (*content.TrailCard, error) {
	args := m.Called(ctx, id)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*content.TrailCard), args.Error(1)
}

func (m *MockTrailSvc) FindAllRegularTrailsCardData(ctx context.Context) ([]*content.TrailCard, error) {
	args := m.Called(ctx)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).([]*content.TrailCard), args.Error(1)
}

func (m *MockTrailSvc) CreateRegularLesson(ctx context.Context, trailID string, lesson *content.Lesson) error {
	args := m.Called(ctx, trailID, lesson)
	return args.Error(0)
}

func (m *MockTrailSvc) FindRegularLesson(ctx context.Context, trailID string, lessonID string) (*content.Lesson, error) {
	args := m.Called(ctx, trailID, lessonID)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*content.Lesson), args.Error(1)
}

func (m *MockTrailSvc) UpdateRegularLesson(ctx context.Context, trailID string, lessonID string, lesson *content.Lesson) error {
	args := m.Called(ctx, trailID, lessonID, lesson)
	return args.Error(0)
}

func (m *MockTrailSvc) DeleteRegularLesson(ctx context.Context, trailID string, lessonID string) error {
	args := m.Called(ctx, trailID, lessonID)
	return args.Error(0)
}

func (m *MockTrailSvc) CreateExtraTrail(ctx context.Context, trail *content.Trail) error {
	args := m.Called(ctx, trail)
	return args.Error(0)
}

func (m *MockTrailSvc) FindExtraTrail(ctx context.Context, id string, userClassification string) (*content.Trail, error) {
	args := m.Called(ctx, id, userClassification)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*content.Trail), args.Error(1)
}

func (m *MockTrailSvc) FindAllExtraTrails(ctx context.Context, userClassification string) ([]*content.Trail, error) {
	args := m.Called(ctx, userClassification)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).([]*content.Trail), args.Error(1)
}

func (m *MockTrailSvc) FindExtraTrailByIdentifier(ctx context.Context, identifier string, userClassification string) (*content.Trail, error) {
	args := m.Called(ctx, identifier, userClassification)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*content.Trail), args.Error(1)
}

func (m *MockTrailSvc) UpdateExtraTrail(ctx context.Context, trail *content.Trail) error {
	args := m.Called(ctx, trail)
	return args.Error(0)
}

func (m *MockTrailSvc) DeleteExtraTrail(ctx context.Context, id string) error {
	args := m.Called(ctx, id)
	return args.Error(0)
}

func (m *MockTrailSvc) FindExtraTrailCardData(ctx context.Context, id string, userClassification string) (*content.TrailCard, error) {
	args := m.Called(ctx, id, userClassification)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*content.TrailCard), args.Error(1)
}

func (m *MockTrailSvc) FindAllExtraTrailsCardData(ctx context.Context, userClassification string) ([]*content.TrailCard, error) {
	args := m.Called(ctx, userClassification)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).([]*content.TrailCard), args.Error(1)
}

func (m *MockTrailSvc) CreateExtraLesson(ctx context.Context, trailID string, userClassification string, lesson *content.Lesson) error {
	args := m.Called(ctx, trailID, userClassification, lesson)
	return args.Error(0)
}

func (m *MockTrailSvc) FindExtraLesson(ctx context.Context, trailID string, userClassification string, lessonID string) (*content.Lesson, error) {
	args := m.Called(ctx, trailID, userClassification, lessonID)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*content.Lesson), args.Error(1)
}

func (m *MockTrailSvc) UpdateExtraLesson(ctx context.Context, trailID string, userClassification string, lessonID string, lesson *content.Lesson) error {
	args := m.Called(ctx, trailID, userClassification, lessonID, lesson)
	return args.Error(0)
}

func (m *MockTrailSvc) DeleteExtraLesson(ctx context.Context, trailID string, userClassification string, lessonID string) error {
	args := m.Called(ctx, trailID, userClassification, lessonID)
	return args.Error(0)
}

func (m *MockTrailSvc) CreateTutorial(ctx context.Context, tutorial *content.Tutorial) error {
	args := m.Called(ctx, tutorial)
	return args.Error(0)
}

func (m *MockTrailSvc) FindTutorial(ctx context.Context, id string) (*content.Tutorial, error) {
	args := m.Called(ctx, id)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*content.Tutorial), args.Error(1)
}

func (m *MockTrailSvc) FindAllTutorials(ctx context.Context) ([]*content.Tutorial, error) {
	args := m.Called(ctx)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).([]*content.Tutorial), args.Error(1)
}

func (m *MockTrailSvc) FindTutorialByIdentifier(ctx context.Context, identifier string) (*content.Tutorial, error) {
	args := m.Called(ctx, identifier)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*content.Tutorial), args.Error(1)
}

func (m *MockTrailSvc) FindTutorialByTicker(ctx context.Context, ticker string) (*content.Tutorial, error) {
	args := m.Called(ctx, ticker)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*content.Tutorial), args.Error(1)
}

func (m *MockTrailSvc) UpdateTutorial(ctx context.Context, tutorial *content.Tutorial) error {
	args := m.Called(ctx, tutorial)
	return args.Error(0)
}

func (m *MockTrailSvc) DeleteTutorial(ctx context.Context, id string) error {
	args := m.Called(ctx, id)
	return args.Error(0)
}

func (m *MockTrailSvc) CreateTutorialLesson(ctx context.Context, tutorialID string, lesson *content.Lesson) error {
	args := m.Called(ctx, tutorialID, lesson)
	return args.Error(0)
}

func (m *MockTrailSvc) FindTutorialLesson(ctx context.Context, tutorialID string, lessonID string) (*content.Lesson, error) {
	args := m.Called(ctx, tutorialID, lessonID)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*content.Lesson), args.Error(1)
}

func (m *MockTrailSvc) UpdateTutorialLesson(ctx context.Context, tutorialID string, lessonID string, lesson *content.Lesson) error {
	args := m.Called(ctx, tutorialID, lessonID, lesson)
	return args.Error(0)
}

func (m *MockTrailSvc) DeleteTutorialLesson(ctx context.Context, tutorialID string, lessonID string) error {
	args := m.Called(ctx, tutorialID, lessonID)
	return args.Error(0)
}

// Mock vault service
type MockVaultSvc struct {
	mock.Mock
}

func (m *MockVaultSvc) Find(ctx context.Context, id string) (*model.Vault, error) {
	args := m.Called(ctx, id)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*model.Vault), args.Error(1)
}

func (m *MockVaultSvc) FindByUser(ctx context.Context, userId string) (*model.Vault, error) {
	args := m.Called(ctx, userId)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*model.Vault), args.Error(1)
}

func (m *MockVaultSvc) Create(ctx context.Context, vault *model.Vault) error {
	args := m.Called(ctx, vault)
	return args.Error(0)
}

func (m *MockVaultSvc) Update(ctx context.Context, vault *model.Vault) error {
	args := m.Called(ctx, mock.Anything)
	return args.Error(0)
}

func (m *MockVaultSvc) Delete(ctx context.Context, id string) error {
	args := m.Called(ctx, id)
	return args.Error(0)
}

func (m *MockVaultSvc) Initialize(ctx context.Context, userId string, referred bool) error {
	args := m.Called(ctx, userId, referred)
	return args.Error(0)
}

// Simple test for progression validation
func TestProgressionBodyValidation(t *testing.T) {
	// Create a valid progression body
	validBody := &progression.ProgressionBody{
		Trail:   "test-trail-id",
		Module:  "lesson-1",
		Content: "lesson-1-content-1",
		Type:    "LESSON",
		Choice: &progression.ModuleContentChoice{
			Identifier: "lesson-1-content-1",
			Next:       "lesson-1-content-2",
		},
	}

	// Test valid progression body
	err := validBody.Validate()
	assert.NoError(t, err)

	// Test invalid type
	invalidTypeBody := &progression.ProgressionBody{
		Trail:   "test-trail-id",
		Module:  "lesson-1",
		Content: "lesson-1-content-1",
		Type:    "INVALID", // Invalid type
		Choice: &progression.ModuleContentChoice{
			Identifier: "lesson-1-content-1",
			Next:       "lesson-1-content-2",
		},
	}
	err = invalidTypeBody.Validate()
	assert.Error(t, err)

	// Test missing trail
	missingTrailBody := &progression.ProgressionBody{
		Trail:   "", // Missing trail
		Module:  "lesson-1",
		Content: "lesson-1-content-1",
		Type:    "LESSON",
		Choice: &progression.ModuleContentChoice{
			Identifier: "lesson-1-content-1",
			Next:       "lesson-1-content-2",
		},
	}
	err = missingTrailBody.Validate()
	assert.Error(t, err)

	// Test missing module
	missingModuleBody := &progression.ProgressionBody{
		Trail:   "test-trail-id",
		Module:  "", // Missing module
		Content: "lesson-1-content-1",
		Type:    "LESSON",
		Choice: &progression.ModuleContentChoice{
			Identifier: "lesson-1-content-1",
			Next:       "lesson-1-content-2",
		},
	}
	err = missingModuleBody.Validate()
	assert.Error(t, err)

	// Note: Content is not validated in the Validate method

	// Test missing choice
	missingChoiceBody := &progression.ProgressionBody{
		Trail:   "test-trail-id",
		Module:  "lesson-1",
		Content: "lesson-1-content-1",
		Type:    "LESSON",
		Choice:  nil, // Missing choice
	}
	err = missingChoiceBody.Validate()
	assert.Error(t, err)
}

// Mock trophy service
type MockTrophySvc struct {
	mock.Mock
}

func (m *MockTrophySvc) FindByRequirement(ctx context.Context, requirement string) (*content.Trophy, error) {
	args := m.Called(ctx, requirement)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*content.Trophy), args.Error(1)
}

func (m *MockTrophySvc) Find(ctx context.Context, id string) (*content.Trophy, error) {
	args := m.Called(ctx, id)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*content.Trophy), args.Error(1)
}

func (m *MockTrophySvc) FindAll(ctx context.Context) ([]*content.Trophy, error) {
	args := m.Called(ctx)
	return args.Get(0).([]*content.Trophy), args.Error(1)
}

func (m *MockTrophySvc) FindByIdentifier(ctx context.Context, identifier string) (*content.Trophy, error) {
	args := m.Called(ctx, identifier)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*content.Trophy), args.Error(1)
}

func (m *MockTrophySvc) Create(ctx context.Context, trophy *content.Trophy) error {
	args := m.Called(ctx, trophy)
	return args.Error(0)
}

func (m *MockTrophySvc) Update(ctx context.Context, trophy *content.Trophy) error {
	args := m.Called(ctx, trophy)
	return args.Error(0)
}

func (m *MockTrophySvc) Delete(ctx context.Context, id string) error {
	args := m.Called(ctx, id)
	return args.Error(0)
}

// Mock cache service
type MockCacheSvc struct {
	mock.Mock
}

func (m *MockCacheSvc) Get(ctx context.Context, key string) (interface{}, bool) {
	args := m.Called(ctx, key)
	return args.Get(0), args.Bool(1)
}

func (m *MockCacheSvc) Set(ctx context.Context, key string, value interface{}, duration time.Duration) error {
	args := m.Called(ctx, key, value, duration)
	return args.Error(0)
}

func (m *MockCacheSvc) Delete(ctx context.Context, key string) error {
	args := m.Called(ctx, key)
	return args.Error(0)
}

// Test for lesson progress tracking
func TestLessonProgress(t *testing.T) {
	// Create mocks
	mockRepo := new(MockProgressionRepo)
	mockTrailSvc := new(MockTrailSvc)
	mockVaultSvc := new(MockVaultSvc)
	mockTrophySvc := new(MockTrophySvc)
	mockCacheSvc := new(MockCacheSvc)

	// Create service
	service := New(
		mockRepo,
		mockTrailSvc,
		mockTrophySvc,
		mockVaultSvc,
		mockCacheSvc,
	)

	// Create test data
	ctx := context.Background()
	userId := "test-user-id"
	trailId := "test-trail-id"

	// Create mock trail
	mockTrail := &content.Trail{
		ID:           trailId,
		Name:         "Test Trail",
		Identifier:   "test-trail",
		Level:        1,
		Logo:         "test-logo.png",
		Color:        "#FFFFFF",
		Requirements: []string{},
		Lessons: []*content.Lesson{
			{
				Name:         "Lesson 1",
				Identifier:   "lesson-1",
				Logo:         "lesson-1-logo.png",
				Color:        "#FF0000",
				Order:        1,
				Requirements: []string{},
				Content: []*content.LessonContent{
					{
						Image:       "image-1.png",
						Identifier:  "lesson-1-content-1",
						Description: "First content of lesson 1",
						Next:        "lesson-1-content-2",
					},
					{
						Image:       "image-2.png",
						Identifier:  "lesson-1-content-2",
						Description: "Second content of lesson 1",
						Next:        "coin", // Completion marker
					},
				},
			},
		},
	}

	// Create mock vault
	mockVault := &model.Vault{
		ObjectID:  primitive.NewObjectID(),
		User:      userId,
		Coins:     0,
		Diamonds:  0,
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}

	// Create mock progression with initial lesson content
	lessonContent := &progression.LessonContent{
		Identifier: "lesson-1-content-1",
		Choice: &progression.ModuleContentChoice{
			Identifier: "lesson-1-content-1",
			Next:       "lesson-1-content-2",
		},
		Timestamp: time.Now(),
	}

	lessonProgression := &progression.Lesson{
		Identifier: "lesson-1",
		Path:       []*progression.LessonContent{lessonContent},
		Current:    "lesson-1-content-2",
		Completed:  false,
		Available:  true,
		Rewarded:   false,
	}

	trailProgression := &progression.Trail{
		ID: trailId,
		Lessons: []*progression.Lesson{
			lessonProgression,
		},
		Current:   "lesson-1",
		Total:     0,
		Available: true,
	}

	mockProgression := &progression.Progression{
		ObjectID:  primitive.NewObjectID(),
		User:      userId,
		Trails:    []*progression.Trail{trailProgression},
		Trophies:  []*progression.Trophy{},
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}

	// Setup mocks
	mockTrailSvc.On("Find", ctx, trailId).Return(mockTrail, nil)
	mockVaultSvc.On("FindByUser", ctx, userId).Return(mockVault, nil)

	// Cache expectations - simulate cache miss for FindByUser
	mockCacheSvc.On("Get", ctx, "progression:user:test-user-id").Return(nil, false)
	mockCacheSvc.On("Set", ctx, "progression:user:test-user-id", mockProgression, time.Hour).Return(nil)
	// Cache invalidation on update
	mockCacheSvc.On("Delete", ctx, "progression:user:test-user-id").Return(nil)

	mockRepo.On("FindByUser", ctx, userId).Return(mockProgression, nil)
	mockVaultSvc.On("Update", ctx, mock.Anything).Return(nil)
	mockRepo.On("Update", ctx, mock.Anything).Return(nil)
	mockTrophySvc.On("FindByRequirement", ctx, trailId).Return(nil, errors.New(errors.Service, "trophy not found", errors.NotFound, nil))

	// Create progression body for lesson completion
	progressionBody := &progression.ProgressionBody{
		Trail:   trailId,
		Module:  "lesson-1",
		Content: "lesson-1-content-2",
		Type:    "LESSON",
		Choice: &progression.ModuleContentChoice{
			Identifier: "lesson-1-content-2",
			Next:       "coin", // Completion marker
		},
	}
	// Test lesson completion
	err := service.CreateLesson(ctx, userId, "standard", progressionBody)
	assert.NoError(t, err)

	// Verify mocks were called
	mockTrailSvc.AssertCalled(t, "Find", ctx, trailId)
	mockVaultSvc.AssertCalled(t, "FindByUser", ctx, userId)
	mockRepo.AssertCalled(t, "FindByUser", ctx, userId)
	mockRepo.AssertCalled(t, "Update", ctx, mock.Anything)
	mockVaultSvc.AssertCalled(t, "Update", ctx, mock.Anything)

	// Verify cache operations
	mockCacheSvc.AssertCalled(t, "Get", ctx, "progression:user:test-user-id")
	mockCacheSvc.AssertCalled(t, "Set", ctx, "progression:user:test-user-id", mockProgression, time.Hour)
	mockCacheSvc.AssertCalled(t, "Delete", ctx, "progression:user:test-user-id")
}

// Test for challenge progress tracking
func TestChallengeProgress(t *testing.T) {
	// Create mocks
	mockRepo := new(MockProgressionRepo)
	mockTrailSvc := new(MockTrailSvc)
	mockVaultSvc := new(MockVaultSvc)
	mockTrophySvc := new(MockTrophySvc)
	mockCacheSvc := new(MockCacheSvc)

	// Create service
	service := New(
		mockRepo,
		mockTrailSvc,
		mockTrophySvc,
		mockVaultSvc,
		mockCacheSvc,
	)

	// Create test data
	ctx := context.Background()
	userId := "test-user-id"
	trailId := "test-trail-id"

	// Create mock trail with challenge
	mockTrail := &content.Trail{
		ID:           trailId,
		Name:         "Test Trail",
		Identifier:   "test-trail",
		Level:        1,
		Logo:         "test-logo.png",
		Color:        "#FFFFFF",
		Requirements: []string{},
		Lessons: []*content.Lesson{
			{
				Name:         "Lesson 1",
				Identifier:   "lesson-1",
				Logo:         "lesson-1-logo.png",
				Color:        "#FF0000",
				Order:        1,
				Requirements: []string{},
				Content: []*content.LessonContent{
					{
						Image:       "image-1.png",
						Identifier:  "lesson-1-content-1",
						Description: "First content of lesson 1",
						Next:        "lesson-1-content-2",
					},
					{
						Image:       "image-2.png",
						Identifier:  "lesson-1-content-2",
						Description: "Second content of lesson 1",
						Next:        "coin", // Completion marker
					},
				},
			},
		},
		Challenge: &content.Challenge{
			Name:        "Test Challenge",
			Identifier:  "test-challenge",
			Description: "Test challenge description",
			Logo:        "challenge-logo.png",
			Color:       "#0000FF",
			Locked:      false,
			Phases: []*content.ChallengePhase{
				{
					Name:         "Phase 1",
					Order:        1,
					Identifier:   "phase-1",
					Requirements: []string{},
					Content: []*content.ChallengeContent{
						{
							Image:       "phase-1-image-1.png",
							Identifier:  "phase-1-content-1",
							Description: "First content of phase 1",
							Next:        "phase-1-content-2",
						},
						{
							Image:       "phase-1-image-2.png",
							Identifier:  "phase-1-content-2",
							Description: "Second content of phase 1",
							Next:        "coin", // Completion marker
						},
					},
				},
			},
		},
	}

	// Create mock vault
	mockVault := &model.Vault{
		ObjectID:  primitive.NewObjectID(),
		User:      userId,
		Coins:     0,
		Diamonds:  0,
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}

	// Create mock progression with completed lessons
	lessonContent1 := &progression.LessonContent{
		Identifier: "lesson-1-content-1",
		Choice: &progression.ModuleContentChoice{
			Identifier: "lesson-1-content-1",
			Next:       "lesson-1-content-2",
		},
		Timestamp: time.Now(),
	}

	lessonContent2 := &progression.LessonContent{
		Identifier: "lesson-1-content-2",
		Choice: &progression.ModuleContentChoice{
			Identifier: "lesson-1-content-2",
			Next:       "coin",
		},
		Timestamp: time.Now(),
	}

	lessonProgression := &progression.Lesson{
		Identifier: "lesson-1",
		Path:       []*progression.LessonContent{lessonContent1, lessonContent2},
		Current:    "coin",
		Completed:  true,
		Available:  true,
		Rewarded:   true,
	}

	challengeContent := &progression.ChallengeContent{
		Identifier: "phase-1-content-1",
		Choice: &progression.ModuleContentChoice{
			Identifier: "phase-1-content-1",
			Next:       "phase-1-content-2",
		},
		Timestamp: time.Now(),
	}

	challengePhase := &progression.ChallengePhase{
		Identifier: "phase-1",
		Path:       []*progression.ChallengeContent{challengeContent},
		Current:    "phase-1-content-2",
		Completed:  false,
		Available:  true,
	}

	challenge := &progression.Challenge{
		Identifier: "test-challenge",
		Phases:     []*progression.ChallengePhase{challengePhase},
		Current:    "phase-1",
		Completed:  false,
		Total:      50,
		Available:  true,
	}

	trailProgression := &progression.Trail{
		ID: trailId,
		Lessons: []*progression.Lesson{
			lessonProgression,
		},
		Challenge:        challenge,
		Current:          "test-challenge",
		Total:            90,
		Available:        true,
		LessonsCompleted: true,
	}

	mockProgression := &progression.Progression{
		ObjectID:  primitive.NewObjectID(),
		User:      userId,
		Trails:    []*progression.Trail{trailProgression},
		Trophies:  []*progression.Trophy{},
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}

	// Setup mocks
	mockTrailSvc.On("Find", ctx, trailId).Return(mockTrail, nil)
	mockVaultSvc.On("FindByUser", ctx, userId).Return(mockVault, nil)

	// Cache expectations - simulate cache miss for FindByUser
	mockCacheSvc.On("Get", ctx, "progression:user:test-user-id").Return(nil, false)
	mockCacheSvc.On("Set", ctx, "progression:user:test-user-id", mockProgression, time.Hour).Return(nil)
	// Cache invalidation on update
	mockCacheSvc.On("Delete", ctx, "progression:user:test-user-id").Return(nil)

	mockRepo.On("FindByUser", ctx, userId).Return(mockProgression, nil)
	mockVaultSvc.On("Update", ctx, mock.Anything).Return(nil)
	mockRepo.On("Update", ctx, mock.Anything).Return(nil)
	mockTrophySvc.On("FindByRequirement", ctx, trailId).Return(nil, errors.New(errors.Service, "trophy not found", errors.NotFound, nil))

	// Create progression body for challenge completion
	progressionBody := &progression.ProgressionBody{
		Trail:   trailId,
		Module:  "phase-1",
		Content: "phase-1-content-2",
		Type:    "CHALLENGE",
		Choice: &progression.ModuleContentChoice{
			Identifier: "phase-1-content-2",
			Next:       "coin", // Completion marker
		},
	}

	// Test challenge completion
	err := service.CreateChallenge(ctx, userId, "standard", progressionBody)
	assert.NoError(t, err)

	// Verify mocks were called
	mockTrailSvc.AssertCalled(t, "Find", ctx, trailId)
	mockVaultSvc.AssertCalled(t, "FindByUser", ctx, userId)
	mockRepo.AssertCalled(t, "FindByUser", ctx, userId)
	mockRepo.AssertCalled(t, "Update", ctx, mock.Anything)
	mockVaultSvc.AssertCalled(t, "Update", ctx, mock.Anything)

	// Verify cache operations
	mockCacheSvc.AssertCalled(t, "Get", ctx, "progression:user:test-user-id")
	mockCacheSvc.AssertCalled(t, "Set", ctx, "progression:user:test-user-id", mockProgression, time.Hour)
	mockCacheSvc.AssertCalled(t, "Delete", ctx, "progression:user:test-user-id")
}

// TestChallengePhaseWithLessonRequirement tests the scenario where a challenge phase has a lesson as a requirement
func TestChallengePhaseWithLessonRequirement(t *testing.T) {
	// Create mocks
	mockRepo := new(MockProgressionRepo)
	mockTrailSvc := new(MockTrailSvc)
	mockVaultSvc := new(MockVaultSvc)
	mockTrophySvc := new(MockTrophySvc)
	mockCacheSvc := new(MockCacheSvc)

	// Create service
	service := New(
		mockRepo,
		mockTrailSvc,
		mockTrophySvc,
		mockVaultSvc,
		mockCacheSvc,
	)

	// Create test data
	ctx := context.Background()
	userId := "test-user-id"
	trailId := "test-trail-id"

	// Create mock vault
	mockVault := &model.Vault{
		ObjectID:  primitive.NewObjectID(),
		User:      userId,
		Coins:     0,
		Diamonds:  0,
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}

	// Create a trail with a lesson and a challenge
	// The challenge phase will have the lesson as a requirement
	lessonId := "passo"
	challengeId := "dna-financeiro"
	phaseId := "dna-financeiro"

	// Create lesson content
	lesson := &content.Lesson{
		Name:       "Test Lesson",
		Identifier: lessonId,
		Logo:       "test-logo.png",
		Color:      "#000000",
		Order:      1,
		Content: []*content.LessonContent{
			{
				Identifier:  "lesson-content-1",
				Description: "Lesson Content 1",
				Next:        "coin", // Lesson ends with a reward
			},
		},
	}

	// Create challenge phase with lesson as requirement
	challengePhase := &content.ChallengePhase{
		Name:         "Test Challenge Phase",
		Identifier:   phaseId,
		Logo:         "test-logo.png",
		Color:        "#000000",
		Order:        1,
		Requirements: []string{lessonId}, // Lesson is a requirement for this phase
		Content: []*content.ChallengeContent{
			{
				Identifier:  "dna-18",
				Description: "Challenge Content 1",
				Choices: []*content.ChallengeChoice{
					{
						Name:       "Choice 1",
						Identifier: "cta-dna",
						Type:       "TEXT",
						Next:       "coin", // Challenge ends with a reward
					},
				},
			},
		},
	}

	// Create challenge
	challenge := &content.Challenge{
		Name:        "Test Challenge",
		Identifier:  challengeId,
		Logo:        "test-logo.png",
		Color:       "#000000",
		Description: "Test Challenge Description",
		Phases:      []*content.ChallengePhase{challengePhase},
	}

	// Create trail
	mockTrail := &content.Trail{
		ID:         trailId,
		Name:       "Test Trail",
		Identifier: "test-trail",
		Logo:       "test-logo.png",
		Color:      "#000000",
		Level:      1,
		Lessons:    []*content.Lesson{lesson},
		Challenge:  challenge,
	}

	// Create progression with completed lesson
	lessonProgression := &progression.Lesson{
		Identifier: lessonId,
		Path: []*progression.LessonContent{
			{
				Identifier: "lesson-content-1",
				Choice: &progression.ModuleContentChoice{
					Identifier: "lesson-content-1",
					Next:       "coin",
				},
				Timestamp: time.Now(),
			},
		},
		Current:   "coin",
		Completed: true, // Lesson is completed
		Available: true,
		Rewarded:  true,
	}

	// Create trail progression
	trailProgression := &progression.Trail{
		ID: trailId,
		Lessons: []*progression.Lesson{
			lessonProgression,
		},
		Current:          lessonId,
		Total:            100,
		Available:        true,
		LessonsCompleted: true, // All lessons are completed
	}

	// Create user progression
	mockProgression := &progression.Progression{
		ObjectID:  primitive.NewObjectID(),
		User:      userId,
		Trails:    []*progression.Trail{trailProgression},
		Trophies:  []*progression.Trophy{},
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}

	// Setup mocks
	mockTrailSvc.On("Find", ctx, trailId).Return(mockTrail, nil)
	mockVaultSvc.On("FindByUser", ctx, userId).Return(mockVault, nil)

	// Cache expectations - simulate cache miss for FindByUser
	mockCacheSvc.On("Get", ctx, "progression:user:test-user-id").Return(nil, false)
	mockCacheSvc.On("Set", ctx, "progression:user:test-user-id", mockProgression, time.Hour).Return(nil)
	// Cache invalidation on update
	mockCacheSvc.On("Delete", ctx, "progression:user:test-user-id").Return(nil)

	mockRepo.On("FindByUser", ctx, userId).Return(mockProgression, nil)
	mockVaultSvc.On("Update", ctx, mock.Anything).Return(nil)
	mockRepo.On("Update", ctx, mock.Anything).Return(nil)

	// Create mock trophy for this trail
	mockTrophy := &content.Trophy{
		ID:          "trophy-" + trailId,
		Name:        "Trail Master: Test Trail",
		Requirement: trailId,
		Identifier:  "trail-master-test-trail",
		Level:       1,
		Logo:        "test-logo.png",
		Conquered:   false,
	}
	mockTrophySvc.On("FindByRequirement", ctx, trailId).Return(mockTrophy, nil)

	// Test case 1: Challenge phase with lesson requirement
	t.Run("Challenge phase with lesson requirement", func(t *testing.T) {
		// Create progression body for challenge
		progressionBody := &progression.ProgressionBody{
			Trail:   trailId,
			Module:  phaseId,
			Content: "dna-18",
			Type:    string(progression.ProgressionTypeChallenge),
			Choice: &progression.ModuleContentChoice{
				Identifier: "cta-dna",
				Next:       "coin", // Completion marker
			},
		}

		// Register challenge progression
		err := service.CreateChallenge(ctx, userId, "standard", progressionBody)
		assert.NoError(t, err)

		// Verify the challenge phase is completed
		// This is done by checking the Update call to the repository
		mockRepo.AssertCalled(t, "Update", ctx, mock.MatchedBy(func(p *progression.Progression) bool {
			// Find the challenge phase
			challengeProgression := p.GetChallengeProgression(trailId)
			if challengeProgression == nil {
				return false
			}

			challengePhase := challengeProgression.GetPhase(phaseId)
			if challengePhase == nil {
				return false
			}

			// Check if the phase is completed
			return challengePhase.Completed
		}))
	})

	// Test case 2: Challenge completion with reward choice
	t.Run("Challenge completion with reward choice", func(t *testing.T) {
		// Reset mocks
		mockRepo.ExpectedCalls = nil
		mockVaultSvc.ExpectedCalls = nil
		mockTrophySvc.ExpectedCalls = nil

		// Setup mocks again
		mockTrailSvc.On("Find", ctx, trailId).Return(mockTrail, nil)
		mockVaultSvc.On("FindByUser", ctx, userId).Return(mockVault, nil)

		// Cache expectations - simulate cache miss for FindByUser
		mockCacheSvc.On("Get", ctx, "progression:user:test-user-id").Return(nil, false)
		mockCacheSvc.On("Set", ctx, "progression:user:test-user-id", mockProgression, time.Hour).Return(nil)
		// Cache invalidation on update
		mockCacheSvc.On("Delete", ctx, "progression:user:test-user-id").Return(nil)

		mockRepo.On("FindByUser", ctx, userId).Return(mockProgression, nil)
		mockVaultSvc.On("Update", ctx, mock.Anything).Return(nil)
		mockRepo.On("Update", ctx, mock.Anything).Return(nil)
		mockTrophySvc.On("FindByRequirement", ctx, trailId).Return(mockTrophy, nil)

		// Create progression body for challenge with reward choice
		progressionBody := &progression.ProgressionBody{
			Trail:   trailId,
			Module:  phaseId,
			Content: "dna-18",
			Type:    string(progression.ProgressionTypeChallenge),
			Choice: &progression.ModuleContentChoice{
				Identifier: "cta-dna",
				Next:       "coin", // Reward choice
			},
		}

		// Register challenge progression
		err := service.CreateChallenge(ctx, userId, "standard", progressionBody)
		assert.NoError(t, err)

		// Verify the challenge is marked as completed
		mockRepo.AssertCalled(t, "Update", ctx, mock.MatchedBy(func(p *progression.Progression) bool {
			// Find the challenge progression
			challengeProgression := p.GetChallengeProgression(trailId)
			if challengeProgression == nil {
				return false
			}

			// Check if the challenge is completed
			return challengeProgression.Completed
		}))

		// Verify the vault is updated
		mockVaultSvc.AssertCalled(t, "Update", ctx, mock.Anything)
	})
}

// TestProgressionCaching tests the caching functionality of the progression service
func TestProgressionCaching(t *testing.T) {
	// Create mocks
	mockRepo := new(MockProgressionRepo)
	mockTrailSvc := new(MockTrailSvc)
	mockVaultSvc := new(MockVaultSvc)
	mockTrophySvc := new(MockTrophySvc)
	mockCacheSvc := new(MockCacheSvc)

	// Create service
	service := New(
		mockRepo,
		mockTrailSvc,
		mockTrophySvc,
		mockVaultSvc,
		mockCacheSvc,
	)

	// Create test data
	ctx := context.Background()
	userId := "test-user-id"

	// Create mock progression
	mockProgression := &progression.Progression{
		ObjectID:  primitive.NewObjectID(),
		User:      userId,
		Trails:    []*progression.Trail{},
		Trophies:  []*progression.Trophy{},
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}
	mockProgression.ID = mockProgression.ObjectID.Hex()

	// Test case 1: Cache miss
	t.Run("Cache miss", func(t *testing.T) {
		// Setup mocks for cache miss
		mockCacheSvc.On("Get", ctx, "progression:user:test-user-id").Return(nil, false).Once()
		mockRepo.On("FindByUser", ctx, userId).Return(mockProgression, nil).Once()
		mockCacheSvc.On("Set", ctx, "progression:user:test-user-id", mockProgression, time.Hour).Return(nil).Once()

		// Call the service
		result, err := service.FindByUser(ctx, userId)

		// Verify results
		assert.NoError(t, err)
		assert.Equal(t, mockProgression, result)

		// Verify mocks were called
		mockCacheSvc.AssertCalled(t, "Get", ctx, "progression:user:test-user-id")
		mockRepo.AssertCalled(t, "FindByUser", ctx, userId)
		mockCacheSvc.AssertCalled(t, "Set", ctx, "progression:user:test-user-id", mockProgression, time.Hour)
	})

	// Test case 2: Cache hit
	t.Run("Cache hit", func(t *testing.T) {
		// Reset mocks
		mockCacheSvc = new(MockCacheSvc)
		mockRepo = new(MockProgressionRepo)

		// Create new service with reset mocks
		service = New(
			mockRepo,
			mockTrailSvc,
			mockTrophySvc,
			mockVaultSvc,
			mockCacheSvc,
		)

		// Setup mocks for cache hit
		mockCacheSvc.On("Get", ctx, "progression:user:test-user-id").Return(mockProgression, true).Once()

		// Call the service
		result, err := service.FindByUser(ctx, userId)

		// Verify results
		assert.NoError(t, err)
		assert.Equal(t, mockProgression, result)

		// Verify mocks were called
		mockCacheSvc.AssertCalled(t, "Get", ctx, "progression:user:test-user-id")
		mockRepo.AssertNotCalled(t, "FindByUser", ctx, userId) // Repository should not be called on cache hit
	})

	// Test case 3: Cache invalidation on update
	t.Run("Cache invalidation on update", func(t *testing.T) {
		// Reset mocks
		mockCacheSvc = new(MockCacheSvc)
		mockRepo = new(MockProgressionRepo)

		// Create new service with reset mocks
		service = New(
			mockRepo,
			mockTrailSvc,
			mockTrophySvc,
			mockVaultSvc,
			mockCacheSvc,
		)

		// Setup mocks for update
		mockRepo.On("Update", ctx, mockProgression).Return(nil).Once()
		mockCacheSvc.On("Delete", ctx, "progression:user:test-user-id").Return(nil).Once()

		// Call the service
		err := service.Update(ctx, mockProgression)

		// Verify results
		assert.NoError(t, err)

		// Verify mocks were called
		mockRepo.AssertCalled(t, "Update", ctx, mockProgression)
		mockCacheSvc.AssertCalled(t, "Delete", ctx, "progression:user:test-user-id")
	})
}
