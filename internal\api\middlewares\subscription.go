package middlewares

import (
	"strings"

	"github.com/dsoplabs/dinbora-backend/internal/errors"
	billingService "github.com/dsoplabs/dinbora-backend/internal/service/billing"
	"github.com/labstack/echo/v4"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// SubscriptionGuardConfig defines the configuration for subscription-based access control
type SubscriptionGuardConfig struct {
	// RequiredFeatures specifies features that the user must have access to
	RequiredFeatures []string
	// RequireActiveSubscription specifies if an active subscription is required
	RequireActiveSubscription bool
	// AllowTrial specifies if trial subscriptions are allowed
	AllowTrial bool
	// SkipPaths specifies paths that should skip subscription checks
	SkipPaths []string
}

// SubscriptionGuard creates a middleware that checks subscription-based access control
func SubscriptionGuard(config SubscriptionGuardConfig, billingService billingService.Service) echo.MiddlewareFunc {
	return func(next echo.HandlerFunc) echo.HandlerFunc {
		return func(c echo.Context) error {
			// Skip subscription check for specified paths
			requestPath := c.Request().URL.Path
			for _, skipPath := range config.SkipPaths {
				if strings.HasPrefix(requestPath, skipPath) {
					return next(c)
				}
			}

			// Get user context (assumes AuthGuard and UserContextMiddleware have run)
			userCtx, err := GetUserContext(c)
			if err != nil {
				return errors.New(errors.Middleware, "user context not found", errors.Unauthorized, err)
			}

			userID, err := primitive.ObjectIDFromHex(userCtx.UserID)
			if err != nil {
				return errors.New(errors.Middleware, "invalid user ID", errors.Validation, err)
			}

			ctx := c.Request().Context()

			// Check if active subscription is required
			if config.RequireActiveSubscription {
				hasActiveSubscription, err := billingService.HasActiveSubscription(ctx, userID)
				if err != nil {
					return errors.New(errors.Middleware, "failed to check subscription status", errors.Internal, err)
				}

				if !hasActiveSubscription {
					return errors.New(errors.Middleware, "active subscription required", errors.Forbidden, nil)
				}
			}

			// Check required features
			if len(config.RequiredFeatures) > 0 {
				userFeatures, err := billingService.GetUserFeatures(ctx, userID)
				if err != nil {
					return errors.New(errors.Middleware, "failed to get user features", errors.Internal, err)
				}

				featuresMap := make(map[string]bool)
				for _, feature := range userFeatures {
					featuresMap[feature] = true
				}

				for _, requiredFeature := range config.RequiredFeatures {
					if !featuresMap[requiredFeature] {
						return errors.New(errors.Middleware, errors.Message("required feature not available: "+requiredFeature), errors.Forbidden, nil)
					}
				}
			}

			return next(c)
		}
	}
}

// RequireActiveSubscription creates a middleware that requires an active subscription
func RequireActiveSubscription(billingService billingService.Service) echo.MiddlewareFunc {
	return SubscriptionGuard(SubscriptionGuardConfig{
		RequireActiveSubscription: true,
		AllowTrial:                true,
	}, billingService)
}

// RequireFeature creates a middleware that requires specific features
func RequireFeature(features []string, billingService billingService.Service) echo.MiddlewareFunc {
	return SubscriptionGuard(SubscriptionGuardConfig{
		RequiredFeatures:          features,
		RequireActiveSubscription: true,
		AllowTrial:                true,
	}, billingService)
}

// RequirePremiumAccess creates a middleware that requires premium subscription access
func RequirePremiumAccess(billingService billingService.Service) echo.MiddlewareFunc {
	return SubscriptionGuard(SubscriptionGuardConfig{
		RequiredFeatures: []string{
			"premium_content",
			"advanced_analytics",
		},
		RequireActiveSubscription: true,
		AllowTrial:                true,
	}, billingService)
}

// SubscriptionContext represents subscription information in the request context
type SubscriptionContext struct {
	HasActiveSubscription bool     `json:"hasActiveSubscription"`
	Features              []string `json:"features"`
	IsInTrial             bool     `json:"isInTrial"`
}

// SubscriptionContextMiddleware adds subscription context to the request
func SubscriptionContextMiddleware(billingService billingService.Service) echo.MiddlewareFunc {
	return func(next echo.HandlerFunc) echo.HandlerFunc {
		return func(c echo.Context) error {
			// Get user context (assumes AuthGuard and UserContextMiddleware have run)
			userCtx, err := GetUserContext(c)
			if err != nil {
				// If no user context, continue without subscription context
				return next(c)
			}

			userID, err := primitive.ObjectIDFromHex(userCtx.UserID)
			if err != nil {
				// If invalid user ID, continue without subscription context
				return next(c)
			}

			ctx := c.Request().Context()

			// Get subscription information
			hasActiveSubscription, _ := billingService.HasActiveSubscription(ctx, userID)
			features, _ := billingService.GetUserFeatures(ctx, userID)

			// Check if user is in trial
			isInTrial := false
			if hasActiveSubscription {
				subscriptions, err := billingService.FindActiveUserSubscriptions(ctx, userID)
				if err == nil {
					for _, subscription := range subscriptions {
						if subscription.IsInTrial() {
							isInTrial = true
							break
						}
					}
				}
			}

			// Create subscription context
			subscriptionCtx := &SubscriptionContext{
				HasActiveSubscription: hasActiveSubscription,
				Features:              features,
				IsInTrial:             isInTrial,
			}

			// Add to request context
			c.Set("subscription_context", subscriptionCtx)

			return next(c)
		}
	}
}

// GetSubscriptionContext retrieves subscription context from the request
func GetSubscriptionContext(c echo.Context) (*SubscriptionContext, error) {
	subscriptionCtx := c.Get("subscription_context")
	if subscriptionCtx == nil {
		return nil, errors.New(errors.Middleware, "subscription context not found", errors.Internal, nil)
	}

	ctx, ok := subscriptionCtx.(*SubscriptionContext)
	if !ok {
		return nil, errors.New(errors.Middleware, "invalid subscription context type", errors.Internal, nil)
	}

	return ctx, nil
}

// HasFeature checks if the current user has a specific feature
func HasFeature(c echo.Context, feature string) bool {
	subscriptionCtx, err := GetSubscriptionContext(c)
	if err != nil {
		return false
	}

	for _, f := range subscriptionCtx.Features {
		if f == feature {
			return true
		}
	}

	return false
}

// HasActiveSubscription checks if the current user has an active subscription
func HasActiveSubscription(c echo.Context) bool {
	subscriptionCtx, err := GetSubscriptionContext(c)
	if err != nil {
		return false
	}

	return subscriptionCtx.HasActiveSubscription
}

// IsInTrial checks if the current user is in a trial period
func IsInTrial(c echo.Context) bool {
	subscriptionCtx, err := GetSubscriptionContext(c)
	if err != nil {
		return false
	}

	return subscriptionCtx.IsInTrial
}
