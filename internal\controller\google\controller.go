package google

import (
	"context"
	"log"
	"net/http"

	"github.com/dsoplabs/dinbora-backend/internal/errors"
	"github.com/dsoplabs/dinbora-backend/internal/model"

	"github.com/dsoplabs/dinbora-backend/internal/service/google"
	"github.com/dsoplabs/dinbora-backend/internal/service/user"
	"github.com/labstack/echo/v4"
)

type Controller interface {
	// Routes
	RegisterRoutes(ctx context.Context, legacyGroup *echo.Group, currentGroup *echo.Group)

	// OAuth
	InitLogin() echo.HandlerFunc
	CallBackFromGoogle() echo.HandlerFunc

	// Register/Login
	CallBackFromGoogleRegister() echo.HandlerFunc
	CallBackFromGoogleLogin() echo.HandlerFunc
}

type controller struct {
	Service google.Service
}

func New(userService user.Service) Controller {
	return &controller{
		Service: google.New(userService),
	}
}

// Routes
func (gc *controller) RegisterRoutes(ctx context.Context, legacyGroup *echo.Group, currentGroup *echo.Group) {
	authGroup := legacyGroup.Group("auth/")

	authGroup.GET("oauth2/google/", gc.InitLogin())
	authGroup.GET("oauth2/callback-google/", gc.CallBackFromGoogle())
	authGroup.POST("oauth2/callback/google/register/", gc.CallBackFromGoogleRegister())
	authGroup.POST("oauth2/callback/google/login/", gc.CallBackFromGoogleLogin())
}

// OAuth
func (gc *controller) InitLogin() echo.HandlerFunc {
	return func(c echo.Context) error {
		url, err := gc.Service.SetupOAuth2()
		if err != nil {
			return errors.HTTPStatus(err)
		}

		http.Redirect(c.Response().Writer, c.Request(), url, http.StatusTemporaryRedirect)

		return c.JSON(http.StatusTemporaryRedirect, "")
	}
}

func (gc *controller) CallBackFromGoogle() echo.HandlerFunc {
	return func(c echo.Context) error {
		state := c.Request().FormValue("state")
		code := c.Request().FormValue("code")

		if state != gc.Service.OauthState() || state == "" {
			http.Redirect(c.Response().Writer, c.Request(), "/", http.StatusTemporaryRedirect)
			return c.JSON(http.StatusBadRequest, "state: not found")
		}

		if code == "" {
			c.Response().Writer.Write([]byte("Code Not Found to provide AccessToken..\n"))
			reason := c.Request().FormValue("error_reason")
			if reason == "user_denied" {
				c.Response().Writer.Write([]byte("User has denied Permission.."))
			}

			http.Redirect(c.Response().Writer, c.Request(), "/", http.StatusTemporaryRedirect)
			return errors.New(errors.Controller, "code not found", errors.Validation, nil)
		}

		token, err := gc.Service.CallBackFromGoogle(code)
		if err != nil {
			http.Redirect(c.Response().Writer, c.Request(), "/", http.StatusTemporaryRedirect)
			return err
		} else if token == nil {
			http.Redirect(c.Response().Writer, c.Request(), "/", http.StatusTemporaryRedirect)
			return errors.New(errors.Controller, "invalid google token", errors.Validation, nil)
		}

		log.Println("Google Access Token:", token.AccessToken)

		googleUserDetails, err := gc.Service.GetUserInfoFromGoogle(token.AccessToken)
		if err != nil {
			http.Redirect(c.Response().Writer, c.Request(), "/", http.StatusTemporaryRedirect)
			return err
		}

		referralCode := c.QueryParam("referral")

		accessData, err := gc.Service.Register(googleUserDetails, &model.Onboarding{}, referralCode)
		if err != nil {
			accessData, err = gc.Service.Login(googleUserDetails)
			if err != nil {
				http.Redirect(c.Response().Writer, c.Request(), "/", http.StatusTemporaryRedirect)
				return err
			}
		}

		log.Println("Dinbora Access Token:", accessData.Access)

		cookie := new(http.Cookie)
		cookie.Name = "logged_in"
		cookie.Value = "true"
		cookie.MaxAge = 24 * 60
		cookie.Path = "/"

		c.SetCookie(cookie)

		cookie = new(http.Cookie)
		cookie.Name = "access_token"
		cookie.Value = accessData.Access
		cookie.MaxAge = 24 * 60
		cookie.Path = "/"
		c.SetCookie(cookie)

		return c.JSON(http.StatusOK, accessData)
	}
}

// Register/Login
func (gc *controller) CallBackFromGoogleRegister() echo.HandlerFunc {
	return func(c echo.Context) error {
		googleRegisterInformation := gc.Service.RegisterInformation()

		referralCode := c.QueryParam("referral")

		if err := c.Bind(&googleRegisterInformation); err != nil {
			http.Redirect(c.Response().Writer, c.Request(), "/", http.StatusTemporaryRedirect)
			return errors.New(errors.Controller, "invalid google token", errors.Validation, nil)
		}

		googleUserDetails, err := gc.Service.GetUserInfoFromGoogle(googleRegisterInformation.Access)
		if err != nil {
			http.Redirect(c.Response().Writer, c.Request(), "/", http.StatusTemporaryRedirect)
			return err
		}

		accessData, err := gc.Service.Register(googleUserDetails, googleRegisterInformation.Onboarding, referralCode)
		if err != nil {
			http.Redirect(c.Response().Writer, c.Request(), "/", http.StatusTemporaryRedirect)
			return err
		}

		return c.JSON(http.StatusOK, accessData)
	}
}

func (gc *controller) CallBackFromGoogleLogin() echo.HandlerFunc {
	return func(c echo.Context) error {
		googleRegisterInformation := gc.Service.RegisterInformation()

		if err := c.Bind(&googleRegisterInformation); err != nil {
			http.Redirect(c.Response().Writer, c.Request(), "/", http.StatusTemporaryRedirect)
			return errors.New(errors.Controller, "invalid google token", errors.Validation, nil)
		}

		googleUserDetails, err := gc.Service.GetUserInfoFromGoogle(googleRegisterInformation.Access)
		if err != nil {
			http.Redirect(c.Response().Writer, c.Request(), "/", http.StatusTemporaryRedirect)
			return err
		}

		accessData, err := gc.Service.Login(googleUserDetails)
		if err != nil {
			http.Redirect(c.Response().Writer, c.Request(), "/", http.StatusTemporaryRedirect)
			return err
		}

		return c.JSON(http.StatusOK, accessData)
	}
}
