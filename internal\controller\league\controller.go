package league

import (
	"context"
	"net/http"
	"time"

	"github.com/dsoplabs/dinbora-backend/internal/api/middlewares"
	"github.com/dsoplabs/dinbora-backend/internal/api/token"
	"github.com/dsoplabs/dinbora-backend/internal/errors"
	_league "github.com/dsoplabs/dinbora-backend/internal/model/league"
	"github.com/dsoplabs/dinbora-backend/internal/service/league"
	_user "github.com/dsoplabs/dinbora-backend/internal/service/user" // Added UserService import
	"github.com/labstack/echo/v4"
)

type Controller interface {
	// Routes
	RegisterRoutes(ctx context.Context, legacyGroup *echo.Group, currentGroup *echo.Group)

	// CRUD
	CreateLeague() echo.HandlerFunc
	FindLeague() echo.HandlerFunc
	FindAllLeagues() echo.HandlerFunc
	PatchLeague() echo.HandlerFunc
	DeleteLeague() echo.HandlerFunc

	// Invitation
	InviteDetails() echo.HandlerFunc
	JoinLeague() echo.HandlerFunc
	LeaveLeague() echo.HandlerFunc

	// Gameplay
	StartNewSeason() echo.HandlerFunc
	FindLeagueRanking() echo.HandlerFunc

	// Cards
	FindLeagueCard() echo.HandlerFunc
	FindAllLeaguesCards() echo.HandlerFunc
	FindAllLeaguesLearning() echo.HandlerFunc
}

type controller struct {
	Service     league.Service
	UserService _user.Service // Added UserService dependency
}

func New(service league.Service, userService _user.Service) Controller { // Added userService parameter
	return &controller{
		Service:     service,
		UserService: userService,
	}
}

// RegisterRoutes connects the league routes to the Echo router group
func (lc *controller) RegisterRoutes(ctx context.Context, legacyGroup *echo.Group, currentGroup *echo.Group) {
	leaguesGroup := currentGroup.Group("/leagues", middlewares.AuthGuard())

	leaguesGroup.POST("", lc.CreateLeague())
	leaguesGroup.GET("/me/:leagueId", lc.FindLeague()) // Changed to /me/:leagueId to indicate user-specific access
	leaguesGroup.GET("/me", lc.FindAllLeagues())
	leaguesGroup.PATCH("/me/:leagueId", lc.PatchLeague())
	leaguesGroup.DELETE("/me/:leagueId", lc.DeleteLeague())

	leaguesGroup.GET("/invites/:code", lc.InviteDetails())
	leaguesGroup.POST("/invites/join", lc.JoinLeague())
	leaguesGroup.POST("/invites/leave", lc.LeaveLeague())

	//leaguesGroup.POST("/me/:leagueId/season", lc.StartNewSeason()) // Evaluate the need for start a season or not
	leaguesGroup.GET("/me/:leagueId/ranking", lc.FindLeagueRanking())

	// Card endpoints
	leaguesGroup.GET("/me/:leagueId/cards", lc.FindLeagueCard())
	leaguesGroup.GET("/me/cards", lc.FindAllLeaguesCards())

	// Learning homepage endpoint
	leaguesGroup.GET("/me/learning", lc.FindAllLeaguesLearning())

	// Note: RecordTransactionStreak is not an explicit endpoint.
	// It's triggered by financialsheet.CreateTransaction.
}

// DTOs for request/response bodies

type LeaveLeagueRequest struct {
	LeagueID string `json:"leagueId" validate:"required"`
}

type StartSeasonRequest struct {
	StartDate time.Time `json:"startDate" validate:"required"`
	EndDate   time.Time `json:"endDate" validate:"required"` // Add validation: EndDate after StartDate
}

// CRUD
func (lc *controller) CreateLeague() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()
		var req CreateLeagueRequest // Use the existing body struct

		if err := c.Bind(&req); err != nil {
			return errors.New(errors.Controller, "invalid_create_league_request_body", errors.BadRequest, err)
		}

		if err := c.Validate(&req); err != nil {
			// errors.Validation is a Kind, so we create a DomainError
			return errors.New(errors.Controller, "create_league_validation_failed", errors.Validation, err)
		}

		userToken, err := token.GetClaimsFromRequest(c.Request())
		if err != nil {
			return err
		}
		userID := userToken.Uid

		// Fetch user details from UserService
		user, err := lc.UserService.Find(ctx, userID) // Changed to use Find method
		if err != nil {
			// Consider how to handle this error: if user not found, can't create league with owner name
			return errors.New(errors.Controller, "failed_to_fetch_owner_profile", errors.Internal, err)
		}
		if user == nil { // Defensive check, though Find should return NotFound error
			return errors.New(errors.Controller, "owner_profile_not_found", errors.NotFound, nil)
		}

		// TODO: Add custom validation for StartDate < EndDate in the request
		if req.StartDate.After(req.EndDate) {
			return errors.New(errors.Controller, "create_league_validation_failed", errors.Validation, nil)
		}

		createdLeague, err := lc.Service.CreateLeague(ctx, userID, user.Name, user.PhotoURL, req.Name, req.StartDate, req.EndDate)
		if err != nil {
			return err
		}
		return c.JSON(http.StatusCreated, createdLeague)
	}
}

func (lc *controller) FindLeague() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()
		leagueID := c.Param("leagueId")

		// Get user ID from token
		userToken, err := token.GetClaimsFromRequest(c.Request())
		if err != nil {
			return err
		}
		userID := userToken.Uid

		// Pass userID to service for access control
		l, err := lc.Service.FindLeague(ctx, leagueID, userID)
		if err != nil {
			return err // Return DomainError directly
		}

		// Fetch the ranking for the league
		ranking, err := lc.Service.FindLeagueRanking(ctx, leagueID, userID)
		if err != nil {
			return err // Return DomainError directly
		}

		// Fetch user photo from the user service for each member
		refetchRankingUserPhotos(ctx, lc.UserService, ranking)

		leagueResponse := &FindResponse{
			ID:              l.ID,
			Code:            l.Code,
			Name:            l.Name,
			BackgroundColor: l.BackgroundColor,
			OwnerUserID:     l.OwnerUserID,
			GeneralStatus: GeneralStatus{
				LeagueDays:        calculateLeagueDaysSinceStart(l.CurrentSeason.StartDate),
				TransactionStreak: calculateUserTransactionStreak(l.Members, userID),
			},
			Podium: ranking.Podium,
			CurrentSeason: _league.Season{
				StartDate: l.CurrentSeason.StartDate,
				EndDate:   l.CurrentSeason.EndDate,
			},
		}

		return c.JSON(http.StatusOK, leagueResponse)
	}
}

func (lc *controller) FindAllLeagues() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()
		userToken, err := token.GetClaimsFromRequest(c.Request())
		if err != nil {
			return err
		}
		userID := userToken.Uid

		userLeagues, serviceErr := lc.Service.FindAllLeagues(ctx, userID)
		if serviceErr != nil {
			return serviceErr // Return DomainError directly
		}

		// Fetch user photo from the user service for each member in each league
		for _, league := range userLeagues {
			refetchLeagueUserPhotos(ctx, lc.UserService, league)
		}

		return c.JSON(http.StatusOK, userLeagues)
	}
}

func (lc *controller) PatchLeague() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()
		userToken, err := token.GetClaimsFromRequest(c.Request())
		if err != nil {
			return err
		}
		userID := userToken.Uid
		leagueID := c.Param("leagueId")

		var req PatchRequest
		if err := c.Bind(&req); err != nil {
			return errors.New(errors.Controller, "invalid_update_league_request_body", errors.BadRequest, err)
		}
		if err := c.Validate(&req); err != nil {
			return errors.New(errors.Controller, "update_league_validation_failed", errors.Validation, err)
		}

		updatedLeague, serviceErr := lc.Service.PatchLeague(ctx, leagueID, userID, req.Name)
		if serviceErr != nil {
			return serviceErr // Return DomainError directly
		}
		return c.JSON(http.StatusOK, updatedLeague)
	}
}

func (lc *controller) DeleteLeague() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()
		userToken, err := token.GetClaimsFromRequest(c.Request())
		if err != nil {
			return err
		}
		userID := userToken.Uid
		leagueID := c.Param("leagueId")

		serviceErr := lc.Service.DeleteLeague(ctx, leagueID, userID)
		if serviceErr != nil {
			return serviceErr // Return DomainError directly
		}
		return c.NoContent(http.StatusNoContent)
	}
}

// Invitation
func (lc *controller) InviteDetails() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()
		code := c.Param("code")

		league, err := lc.Service.InviteDetails(ctx, code)
		if err != nil {
			return err // Return DomainError directly
		}

		// Fetch owner user details from UserService
		owner, err := lc.UserService.Find(ctx, league.OwnerUserID)
		if err != nil {
			return err // Return DomainError directly
		}

		response := InviteDetailsResponse{
			Name:           league.Name,
			OwnerUserName:  owner.Name,
			SeasonDuration: int(league.CurrentSeason.EndDate.Sub(league.CurrentSeason.StartDate).Hours() / 24),
		}

		return c.JSON(http.StatusOK, response)
	}
}

func (lc *controller) JoinLeague() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()
		userToken, err := token.GetClaimsFromRequest(c.Request())
		if err != nil {
			return err
		}
		userID := userToken.Uid

		var req JoinLeagueRequest
		if err := c.Bind(&req); err != nil {
			return errors.New(errors.Controller, "invalid_join_league_request_body", errors.BadRequest, err)
		}
		if err := c.Validate(&req); err != nil {
			return errors.New(errors.Controller, "join_league_validation_failed", errors.Validation, err)
		}
		inviteCode := req.Code

		// Validate that we have an invite code
		if inviteCode == "" {
			return errors.New(errors.Controller, "invite_code_required", errors.BadRequest, nil)
		}

		// Fetch user details from UserService
		user, err := lc.UserService.Find(ctx, userID) // Changed to use Find method
		if err != nil {
			// Consider how to handle this error: if user not found, can't join league with user name/photo
			return errors.New(errors.Controller, "failed_to_fetch_user_profile_for_join", errors.Internal, err)
		}
		if user == nil { // Defensive check
			return errors.New(errors.Controller, "user_profile_not_found_for_join", errors.NotFound, nil)
		}

		joinedLeague, err := lc.Service.JoinLeague(ctx, userID, user.Name, user.PhotoURL, inviteCode)
		if err != nil {
			return err // Return DomainError directly
		}
		return c.JSON(http.StatusOK, joinedLeague)
	}
}

func (lc *controller) LeaveLeague() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()
		userToken, err := token.GetClaimsFromRequest(c.Request())
		if err != nil {
			return err
		}
		userID := userToken.Uid

		var req LeaveLeagueRequest
		if err := c.Bind(&req); err != nil {
			return errors.New(errors.Controller, "invalid_leave_league_request_body", errors.BadRequest, err)
		}
		if err := c.Validate(&req); err != nil {
			return errors.New(errors.Controller, "leave_league_validation_failed", errors.Validation, err)
		}

		if err := lc.Service.LeaveLeague(ctx, req.LeagueID, userID); err != nil {
			return err // Return DomainError directly
		}
		return c.NoContent(http.StatusNoContent)
	}
}

// Gameplay
func (lc *controller) StartNewSeason() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()
		userToken, err := token.GetClaimsFromRequest(c.Request())
		if err != nil {
			return err
		}
		userID := userToken.Uid
		leagueID := c.Param("leagueId")

		var req StartSeasonRequest
		if err := c.Bind(&req); err != nil {
			return errors.New(errors.Controller, "invalid_start_season_request_body", errors.BadRequest, err)
		}
		// TODO: Add custom validation for StartDate < EndDate
		if err := c.Validate(&req); err != nil {
			return errors.New(errors.Controller, "start_season_validation_failed", errors.Validation, err)
		}

		updatedLeague, serviceErr := lc.Service.StartNewSeasonInLeague(ctx, leagueID, userID, req.StartDate, req.EndDate)
		if serviceErr != nil {
			return serviceErr // Return DomainError directly
		}
		return c.JSON(http.StatusOK, updatedLeague)
	}
}

func (lc *controller) FindLeagueRanking() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()
		leagueID := c.Param("leagueId")

		// Get user ID from token
		userToken, err := token.GetClaimsFromRequest(c.Request())
		if err != nil {
			return err
		}
		userID := userToken.Uid

		// Pass userID to service for access control
		ranking, err := lc.Service.FindLeagueRanking(ctx, leagueID, userID)
		if err != nil {
			return err // Return DomainError directly
		}

		// Fetch user photo from the user service for each member
		refetchRankingUserPhotos(ctx, lc.UserService, ranking)

		return c.JSON(http.StatusOK, ranking)
	}
}

// Cards
func (lc *controller) FindLeagueCard() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()
		leagueID := c.Param("leagueId")

		// Get user ID from token
		userToken, err := token.GetClaimsFromRequest(c.Request())
		if err != nil {
			return err
		}
		userID := userToken.Uid

		// Pass userID to service for access control
		card, err := lc.Service.FindLeagueCard(ctx, leagueID, userID)
		if err != nil {
			return err // Return DomainError directly
		}
		return c.JSON(http.StatusOK, card)
	}
}

func (lc *controller) FindAllLeaguesCards() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()
		// Get user ID from token
		userToken, err := token.GetClaimsFromRequest(c.Request())
		if err != nil {
			return err
		}

		// Get all league cards for the user
		cards, err := lc.Service.FindAllLeaguesCards(ctx, userToken.Uid)
		if err != nil {
			return err // Return DomainError directly
		}

		return c.JSON(http.StatusOK, cards)
	}
}

func (lc *controller) FindAllLeaguesLearning() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()

		// Get user ID from token
		userToken, err := token.GetClaimsFromRequest(c.Request())
		if err != nil {
			return err
		}

		// Get the league id from the request
		leagueID := c.QueryParam("league")

		// Get all league cards for the user
		cards, err := lc.Service.FindAllLeaguesCards(ctx, userToken.Uid)
		if err != nil {
			return err // Return DomainError directly
		}

		// If the param is empty get the podium from the first card
		if leagueID == "" {
			if len(cards) > 0 {
				leagueID = cards[0].ID
			}
		}

		// Get the podium for the league
		ranking, err := lc.Service.FindLeagueRanking(ctx, leagueID, userToken.Uid)
		if err != nil {
			return err // Return DomainError directly
		}

		// Fetch user photo from the user service for each member
		refetchRankingUserPhotos(ctx, lc.UserService, ranking)

		// Build the FindAllLeagueCardsLearningResponse
		var leagueCardsLearning []*LeagueLearning
		for _, card := range cards {
			leagueCardsLearning = append(leagueCardsLearning, &LeagueLearning{
				ID:   card.ID,
				Name: card.Name,
			})
		}
		response := &FindAllLeagueLearningResponse{
			LeaguesLearning: leagueCardsLearning,
			Podium:          ranking.Podium,
		}

		return c.JSON(http.StatusOK, response)
	}
}

// Helper
func calculateLeagueDaysSinceStart(startDate time.Time) int {
	now := time.Now()
	if now.Before(startDate) {
		return 0
	}
	return int(now.Sub(startDate).Hours() / 24)
}

func calculateUserTransactionStreak(members []_league.LeagueMember, userID string) int {
	for _, member := range members {
		if member.UserID == userID {
			return member.TransactionStreak
		}
	}
	return 0
}

// refetchLeagueUserPhotos fetches the user photo from the user service for each member in the league
func refetchLeagueUserPhotos(ctx context.Context, userService _user.Service, league *_league.League) {
	for i, member := range league.Members {
		user, err := userService.Find(ctx, member.UserID)
		if err != nil {
			continue // Skip if user not found
		}
		if user != nil {
			league.Members[i].PhotoURL = user.PhotoURL
		}
	}
}

// refetchRankingUserPhotos fetches the user photo from the user service for each member in the league
func refetchRankingUserPhotos(ctx context.Context, userService _user.Service, ranking *_league.LeagueRanking) {
	for _, userRanking := range ranking.Podium {
		user, err := userService.Find(ctx, userRanking.UserID)
		if err != nil {
			continue // Skip if user not found
		}
		if user != nil {
			userRanking.PhotoURL = user.PhotoURL
		}
	}
	for _, userRanking := range ranking.General {
		user, err := userService.Find(ctx, userRanking.UserID)
		if err != nil {
			continue // Skip if user not found
		}
		if user != nil {
			userRanking.PhotoURL = user.PhotoURL
		}
	}
}
