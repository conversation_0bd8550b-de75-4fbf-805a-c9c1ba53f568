# Technical Context

## Technology Stack

### Core Technologies
1. Go (Backend Language)
   - Clean architecture implementation
   - Standard library for HTTP handling
   - Built-in concurrency support
   - Strong type system

2. MongoDB
   - Primary database
   - Document-based storage
   - Collection organization
   - Flexible schema support

### Development Tools
1. Air
   - Live reload functionality
   - Development workflow enhancement
   - Configuration via .air.toml

2. Make
   - Build automation
   - Development commands
   - Deployment scripts
   - Testing automation

3. Docker
   - Container-based development
   - Service orchestration
   - Development environment consistency
   - Production deployment support

## Project Structure

### Core Directories
```
dinbora-backend/
├── cmd/                    # Application entry points
├── config/                 # Configuration management
├── data/                   # Data layer implementations
├── docker/                 # Docker configurations
├── docs/                   # Documentation
├── internal/               # Internal packages
│   ├── api/               # API layer
│   ├── controller/        # Request handlers
│   ├── model/            # Domain models
│   ├── repository/       # Data access
│   └── service/          # Business logic
├── migration/             # Data migrations
└── scripts/              # Utility scripts
```

## Development Setup

### Prerequisites
- Go 1.x
- Docker and Docker Compose
- Make
- MongoDB
- Redis

### Environment Configuration
1. Development (.env)
   - Local development settings
   - Debug configurations
   - Test endpoints

2. Production (set by the server)
   - Production settings
   - Security configurations
   - Performance tuning

### Build Process
1. Development
   ```bash
   make dev      # Start development server
   make test     # Run tests
   make lint     # Run linters
   ```

2. Production
   ```bash
   make build    # Build production binary
   make deploy   # Deploy to production
   ```

## Dependencies

### Core Libraries
1. Database
   - MongoDB driver (collections improved/optimized)
   - Redis client
   - Connection pooling
   - Content Trail cache check implemented

2. Authentication
   - JWT handling
   - OAuth2 clients
   - Password hashing

3. API
   - Router implementation
   - Middleware chain
   - Response formatting

### External Services
1. Payment Processing
   - Stripe API integration
   - Webhook handling
   - Payment flow management

2. Authentication Providers
   - Google OAuth
   - Facebook OAuth
   - Apple Sign In

3. Notification Services
   - Email delivery
   - Push notifications
   - SMS (if applicable)

## Technical Constraints

### Performance Requirements
- Response time < 200ms for API endpoints
- Concurrent user support
- Efficient database queries
- Proper indexing

### Security Requirements
- HTTPS enforcement
- JWT validation
- Input sanitization
- Rate limiting
- CORS configuration

### Scalability Requirements
- Horizontal scaling capability
- Load balancing support
- Cache optimization
- Connection pooling

## Monitoring and Logging

### Logging
- Structured logging format
- Log levels (DEBUG, INFO, ERROR)
- Request/Response logging
- Error tracking

### Monitoring
- Health check endpoints
- Performance metrics
- Error rate monitoring
- Resource utilization

## Testing Strategy

### Test Types
1. Unit Tests
   - Business logic
   - Model validation
   - Utility functions

2. Integration Tests
   - API endpoints
   - Database operations
   - External services

3. Performance Tests
   - Load testing
   - Stress testing
   - Concurrency testing

## Deployment

### Environments
1. Development
   - Local development
   - Feature testing
   - Integration testing

2. Production
   - Live environment
   - Customer-facing
   - Performance optimized

### CI/CD Pipeline
- Automated testing
- Build verification
- Deployment automation (recent improvements implemented)
- Environment configuration
