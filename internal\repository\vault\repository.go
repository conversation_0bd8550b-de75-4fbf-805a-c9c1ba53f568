package vault

import (
	"context"
	"github.com/dsoplabs/dinbora-backend/internal/model"
)

type Reader interface {
	Find(ctx context.Context, id string) (*model.Vault, error)
	FindByUser(ctx context.Context, userId string) (*model.Vault, error)
}

type Writer interface {
	Create(ctx context.Context, vault *model.Vault) error
	Update(ctx context.Context, vault *model.Vault) error
	Delete(ctx context.Context, id string) error
}

type Repository interface {
	Reader
	Writer
}
