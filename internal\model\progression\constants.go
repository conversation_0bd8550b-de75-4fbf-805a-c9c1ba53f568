package progression

// ProgressionType represents the type of progression (lesson or challenge)
type ProgressionType string

const (
	// ProgressionTypeLesson represents a lesson progression
	ProgressionTypeLesson ProgressionType = "LESSON"
	
	// ProgressionTypeChallenge represents a challenge progression
	ProgressionTypeChallenge ProgressionType = "CHALLENGE"
)

// RewardType represents the type of reward (coin or diamond)
type RewardType string

const (
	// RewardTypeCoin represents a coin reward
	RewardTypeCoin RewardType = "coin"
	
	// RewardTypeDiamond represents a diamond reward
	RewardTypeDiamond RewardType = "diamond"
)
