package trophy

import (
	"context"
	"log"

	"github.com/dsoplabs/dinbora-backend/internal/errors"
	"github.com/dsoplabs/dinbora-backend/internal/model/content"
	"github.com/dsoplabs/dinbora-backend/internal/repository"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

type mongoDB struct {
	collection *mongo.Collection
}

func New(db *mongo.Database) Repository {
	repo := &mongoDB{
		collection: db.Collection(repository.TROPHIES_COLLECTION),
	}

	// Create new index
	_, err := repo.collection.Indexes().CreateOne(
		context.Background(),
		mongo.IndexModel{
			Keys:    bson.D{{Key: "requirement", Value: 1}},
			Options: options.Index().SetUnique(true).SetName("requirement"),
		},
	)
	if err != nil {
		// Log error but don't fail - index might already exist
		log.Println("warning: failed to create index on trophy.requirement field")
		db.Client().Disconnect(context.Background())
	}

	return repo
}

func (m mongoDB) Create(ctx context.Context, trophy *content.Trophy) error {
	_, err := m.collection.InsertOne(ctx, trophy)
	if err != nil {
		if mongo.IsDuplicateKeyError(err) {
			return errors.New(errors.Repository, "trophy already exists", errors.Conflict, err)
		}
		return errors.New(errors.Repository, "failed to create trophy", errors.Internal, err)
	}

	return nil
}

func (m mongoDB) Find(ctx context.Context, id string) (*content.Trophy, error) {
	objectId, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return nil, errors.New(errors.Repository, "invalid ID format", errors.Validation, err)
	}

	var trophy content.Trophy
	if err = m.collection.FindOne(ctx,
		bson.D{primitive.E{Key: "_id", Value: objectId}}).Decode(&trophy); err != nil {
		if err == mongo.ErrNoDocuments {
			return nil, errors.New(errors.Repository, "trophy not found", errors.NotFound, err)
		}
		return nil, errors.New(errors.Repository, "failed to find trophy", errors.Internal, err)
	}

	return &trophy, nil
}

func (m mongoDB) FindAll(ctx context.Context) ([]*content.Trophy, error) {
	cursor, err := m.collection.Find(ctx, bson.D{}, nil)
	if err != nil {
		return nil, errors.New(errors.Repository, "failed to query trophies", errors.Internal, err)
	}
	defer cursor.Close(ctx)

	var trophies []*content.Trophy
	for cursor.Next(ctx) {
		var trophy content.Trophy
		if err = cursor.Decode(&trophy); err != nil {
			return nil, errors.New(errors.Repository, "failed to decode trophy", errors.Internal, err)
		}
		trophies = append(trophies, &trophy)
	}

	return trophies, nil
}

func (m mongoDB) FindByIdentifier(ctx context.Context, identifier string) (*content.Trophy, error) {
	var trophy content.Trophy
	if err := m.collection.FindOne(ctx,
		bson.D{primitive.E{Key: "identifier", Value: identifier}}).Decode(&trophy); err != nil {
		if err == mongo.ErrNoDocuments {
			return nil, errors.New(errors.Repository, "trophy not found by identifier", errors.NotFound, err)
		}
		return nil, errors.New(errors.Repository, "failed to find trophy by identifier", errors.Internal, err)
	}

	return &trophy, nil
}

func (m mongoDB) FindByRequirement(ctx context.Context, requirement string) (*content.Trophy, error) {
	var trophy content.Trophy
	if err := m.collection.FindOne(ctx,
		bson.D{primitive.E{Key: "requirement", Value: requirement}}).Decode(&trophy); err != nil {
		if err == mongo.ErrNoDocuments {
			return nil, errors.New(errors.Repository, "trophy not found by requirement", errors.NotFound, err)
		}
		return nil, errors.New(errors.Repository, "failed to find trophy by requirement", errors.Internal, err)
	}

	return &trophy, nil
}

func (m mongoDB) Update(ctx context.Context, trophy *content.Trophy) error {
	result, err := m.collection.UpdateOne(ctx,
		bson.D{primitive.E{Key: "_id", Value: trophy.ObjectID}},
		primitive.M{"$set": trophy})
	if err != nil {
		if mongo.IsDuplicateKeyError(err) {
			return errors.New(errors.Repository, "trophy update would cause conflict", errors.Conflict, err)
		}
		return errors.New(errors.Repository, "failed to update trophy", errors.Internal, err)
	}

	if result.MatchedCount <= 0 {
		return errors.New(errors.Repository, "trophy not found for update", errors.NotFound, nil)
	}

	return nil
}

func (m mongoDB) Delete(ctx context.Context, id string) error {
	objectID, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return errors.New(errors.Repository, "invalid ID format", errors.Validation, err)
	}

	result, err := m.collection.DeleteOne(ctx,
		bson.D{primitive.E{Key: "_id", Value: objectID}})
	if err != nil {
		return err
	}

	if result.DeletedCount <= 0 {
		return errors.New(errors.Repository, "trophy not found for deletion", errors.NotFound, nil)
	}

	return nil
}
