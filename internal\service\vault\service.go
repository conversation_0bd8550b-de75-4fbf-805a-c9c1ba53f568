package vault

import (
	"context"
	"time"

	"github.com/dsoplabs/dinbora-backend/internal/errors"
	"github.com/dsoplabs/dinbora-backend/internal/model"
	"github.com/dsoplabs/dinbora-backend/internal/repository/vault"
)

type Service interface {
	// CRUD
	Initialize(ctx context.Context, userId string, referred bool) error
	Find(ctx context.Context, id string) (*model.Vault, error)
	FindByUser(ctx context.Context, userId string) (*model.Vault, error)
	Update(ctx context.Context, vault *model.Vault) error
	Delete(ctx context.Context, id string) error
}

type service struct {
	Repository vault.Repository
}

func New(repository vault.Repository) Service {
	return &service{
		Repository: repository,
	}
}

// CRUD
func (s *service) Initialize(ctx context.Context, userId string, referred bool) error {
	foundVault, err := s.FindByUser(ctx, userId)
	if err != nil && err.(*errors.DomainError).Kind() != errors.NotFound {
		return err
	} else if foundVault != nil {
		return err
	}

	currentTime := time.Now()
	newVault := &model.Vault{
		User:      userId,
		Coins:     0,
		Diamonds:  0,
		CreatedAt: currentTime,
		UpdatedAt: currentTime,
	}

	if referred {
		//newVault.Diamonds
		newVault.Coins = 10
	}

	return s.Repository.Create(ctx, newVault)
}

func (s *service) Find(ctx context.Context, id string) (*model.Vault, error) {
	foundVault, err := s.Repository.Find(ctx, id)
	if err != nil {
		return nil, err
	}
	foundVault.ID = foundVault.ObjectID.Hex()
	return foundVault, nil
}

func (s *service) FindByUser(ctx context.Context, userId string) (*model.Vault, error) {
	foundVault, err := s.Repository.FindByUser(ctx, userId)
	if err != nil {
		return nil, err
	}
	foundVault.ID = foundVault.ObjectID.Hex()
	return foundVault, nil
}

func (s *service) Update(ctx context.Context, vault *model.Vault) error {
	return s.Repository.Update(ctx, vault)
}

func (s *service) Delete(ctx context.Context, id string) error {
	return s.Repository.Delete(ctx, id)
}
