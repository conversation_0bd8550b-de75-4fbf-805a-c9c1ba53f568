package ticker

import (
	"context"
	"os"
	"testing"
	"time"

	"github.com/dsoplabs/dinbora-backend/config"
	"github.com/dsoplabs/dinbora-backend/internal/model/content"
	"github.com/dsoplabs/dinbora-backend/internal/repository"
	"github.com/stretchr/testify/suite"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

type MongoRepositoryTestSuite struct {
	suite.Suite
	client     *mongo.Client
	db         *mongo.Database
	repository Repository
	testIDs    []string
}

func (suite *MongoRepositoryTestSuite) SetupTest() {
	ctx := context.Background()

	// Initialize environment configuration
	err := config.EnvFromFile("../../../../.env")
	if err != nil {
		suite.T().Skipf("Failed to initialize environment configuration: %v - skipping integration tests", err)
		return
	}

	// Get database configuration
	dbURL := os.Getenv("DATABASE_URL")
	dbName := os.Getenv("DATABASE_NAME")

	if dbURL == "" || dbName == "" {
		suite.T().Skip("DATABASE_URL and DATABASE_NAME configuration not set - skipping integration tests")
		return
	}

	// Connect to MongoDB
	clientOptions := options.Client().ApplyURI(dbURL)
	client, err := mongo.Connect(ctx, clientOptions)
	if err != nil {
		suite.T().Skipf("Failed to connect to MongoDB: %v - skipping integration tests", err)
		return
	}
	suite.client = client

	// Verify MongoDB connection
	err = suite.client.Ping(ctx, nil)
	if err != nil {
		suite.T().Skipf("Failed to ping MongoDB: %v - skipping integration tests", err)
		return
	}

	suite.db = suite.client.Database(dbName)
	suite.repository = New(suite.db)

	// Verify test collection exists
	collections, err := suite.db.ListCollectionNames(ctx, bson.M{})
	if err != nil {
		suite.T().Skipf("Failed to list collections: %v - skipping integration tests", err)
		return
	}

	var collectionExists bool
	for _, col := range collections {
		if col == repository.TICKERS_COLLECTION {
			collectionExists = true
			break
		}
	}

	if !collectionExists {
		suite.T().Skipf("Collection %s does not exist - skipping integration tests", repository.TICKERS_COLLECTION)
		return
	}
}

func (suite *MongoRepositoryTestSuite) TearDownTest() {
	ctx := context.Background()

	// Clean up test data
	if suite.db != nil && len(suite.testIDs) > 0 {
		for _, id := range suite.testIDs {
			objID, err := primitive.ObjectIDFromHex(id)
			if err == nil {
				suite.T().Logf("Deleting test ticker with ID: %s", id)
				res, err := suite.db.Collection(repository.TICKERS_COLLECTION).DeleteOne(ctx, bson.M{"_id": objID})
				suite.NoError(err)
				suite.T().Logf("Delete result: %d document(s) deleted", res.DeletedCount)
			}
		}
	}

	// Disconnect client
	if suite.client != nil {
		err := suite.client.Disconnect(ctx)
		if err != nil {
			suite.T().Errorf("Error disconnecting client: %v", err)
		}
	}
}

func TestMongoRepositoryTestSuite(t *testing.T) {
	suite.Run(t, new(MongoRepositoryTestSuite))
}

func (suite *MongoRepositoryTestSuite) createTestTicker(name, identifier, group, category, tickerType string) *content.Ticker {
	now := time.Now()
	ticker := &content.Ticker{
		ObjectID:   primitive.NewObjectID(),
		Name:       name,
		Identifier: identifier,
		Group:      group,
		Category:   category,
		Type:       tickerType,
		CreatedAt:  now,
		UpdatedAt:  now,
	}
	return ticker
}

func (suite *MongoRepositoryTestSuite) TestIndexCreation() {
	ctx := context.Background()

	// Get indexes
	cursor, err := suite.db.Collection(repository.TICKERS_COLLECTION).Indexes().List(ctx)
	suite.Require().NoError(err)
	defer cursor.Close(ctx)

	var indexes []bson.M
	err = cursor.All(ctx, &indexes)
	suite.Require().NoError(err)

	// Find our specific index
	var found bool
	for _, index := range indexes {
		if index["name"] == "identifier" {
			found = true
			// Verify index properties
			key := index["key"].(bson.M)
			suite.Equal(int32(1), key["identifier"]) // MongoDB returns int32 for index value
			suite.Equal(true, index["unique"])
			break
		}
	}
	suite.True(found, "Identifier index not found")
}

func (suite *MongoRepositoryTestSuite) TestCRUDOperations() {
	ctx := context.Background()

	// Create
	ticker := suite.createTestTicker(
		"Test Ticker",
		"test_ticker",
		"Test Group",
		"Test Category",
		"Test Type",
	)

	// Prepare ticker for creation (this will handle identifier uppercase conversion)
	err := ticker.PrepareCreate()
	suite.Require().NoError(err)

	err = suite.repository.Create(ctx, ticker)
	suite.Require().NoError(err)
	suite.testIDs = append(suite.testIDs, ticker.ObjectID.Hex())

	// Verify identifier converted to uppercase
	found, err := suite.repository.Find(ctx, ticker.ObjectID.Hex())
	suite.Require().NoError(err)
	suite.Equal("TEST_TICKER", found.Identifier)

	// Find by ID
	found, err = suite.repository.Find(ctx, ticker.ObjectID.Hex())
	suite.Require().NoError(err)
	suite.Equal(ticker.Name, found.Name)
	suite.Equal(ticker.Group, found.Group)
	suite.Equal(ticker.Category, found.Category)

	// Find by Identifier
	found, err = suite.repository.FindByIdentifier(ctx, "TEST_TICKER")
	suite.Require().NoError(err)
	suite.Equal(ticker.ObjectID, found.ObjectID)

	// Find by Category
	tickers, err := suite.repository.FindByCategory(ctx, ticker.Category)
	suite.Require().NoError(err)
	suite.Len(tickers, 1)
	suite.Equal(ticker.Name, tickers[0].Name)

	// Update
	ticker.Name = "Updated Test Ticker"
	err = suite.repository.Update(ctx, ticker)
	suite.Require().NoError(err)

	// Verify update
	updated, err := suite.repository.Find(ctx, ticker.ObjectID.Hex())
	suite.Require().NoError(err)
	suite.Equal("Updated Test Ticker", updated.Name)

	// Delete
	err = suite.repository.Delete(ctx, ticker.ObjectID.Hex())
	suite.Require().NoError(err)

	// Verify deletion
	_, err = suite.repository.Find(ctx, ticker.ObjectID.Hex())
	suite.Error(err)
}

func (suite *MongoRepositoryTestSuite) TestUniqueIdentifierConstraint() {
	ctx := context.Background()

	// Create first ticker
	ticker1 := suite.createTestTicker(
		"Test Ticker 1",
		"test_ticker",
		"Test Group",
		"Test Category",
		"Test Type",
	)

	err := ticker1.PrepareCreate()
	suite.Require().NoError(err)

	err = suite.repository.Create(ctx, ticker1)
	suite.Require().NoError(err)
	suite.testIDs = append(suite.testIDs, ticker1.ObjectID.Hex())

	// Try to create another ticker with same identifier
	ticker2 := suite.createTestTicker(
		"Test Ticker 2",
		"test_ticker", // Same identifier
		"Test Group 2",
		"Test Category 2",
		"Test Type 2",
	)

	err = ticker2.PrepareCreate()
	suite.Require().NoError(err)

	err = suite.repository.Create(ctx, ticker2)
	suite.Error(err) // Should fail due to unique constraint
}

func (suite *MongoRepositoryTestSuite) TestFindByCategory() {
	ctx := context.Background()
	category := "Test Category Multiple"

	// Create multiple tickers in same category
	for i := 0; i < 3; i++ {
		ticker := suite.createTestTicker(
			"Test Ticker "+string(rune(i+'1')),
			"test_ticker_"+string(rune(i+'1')),
			"Test Group",
			category,
			"Test Type",
		)

		err := ticker.PrepareCreate()
		suite.Require().NoError(err)

		err = suite.repository.Create(ctx, ticker)
		suite.Require().NoError(err)
		suite.testIDs = append(suite.testIDs, ticker.ObjectID.Hex())
	}

	// Find all by category
	tickers, err := suite.repository.FindByCategory(ctx, category)
	suite.Require().NoError(err)
	suite.Len(tickers, 3)
}

func (suite *MongoRepositoryTestSuite) TestFindAll() {
	ctx := context.Background()

	// Initially should be empty (after cleanup)
	tickers, err := suite.repository.FindAll(ctx)
	suite.Require().NoError(err)
	initialCount := len(tickers)

	// Create multiple tickers
	for i := 0; i < 3; i++ {
		ticker := suite.createTestTicker(
			"Test Ticker "+string(rune(i+'1')),
			"all_test_ticker_"+string(rune(i+'1')),
			"Test Group",
			"Test Category",
			"Test Type",
		)

		err := ticker.PrepareCreate()
		suite.Require().NoError(err)

		err = suite.repository.Create(ctx, ticker)
		suite.Require().NoError(err)
		suite.testIDs = append(suite.testIDs, ticker.ObjectID.Hex())
	}

	// Find all
	tickers, err = suite.repository.FindAll(ctx)
	suite.Require().NoError(err)
	suite.Len(tickers, initialCount+3)
}

func (suite *MongoRepositoryTestSuite) TestErrorCases() {
	ctx := context.Background()

	// Test invalid ID format
	_, err := suite.repository.Find(ctx, "invalid-id")
	suite.Error(err)

	// Test not found
	_, err = suite.repository.Find(ctx, primitive.NewObjectID().Hex())
	suite.Error(err)

	// Test find by non-existent identifier
	_, err = suite.repository.FindByIdentifier(ctx, "non_existent")
	suite.Error(err)

	// Test create with missing required fields
	ticker := &content.Ticker{
		ObjectID: primitive.NewObjectID(),
	}
	err = suite.repository.Create(ctx, ticker)
	suite.Error(err)

	// Test update non-existent ticker
	ticker = suite.createTestTicker(
		"Test Ticker",
		"test_ticker_update",
		"Test Group",
		"Test Category",
		"Test Type",
	)
	ticker.ObjectID = primitive.NewObjectID() // Ensure non-existent ID
	err = suite.repository.Update(ctx, ticker)
	suite.Error(err)

	// Test delete with empty ID
	err = suite.repository.Delete(ctx, "")
	suite.Error(err)
}
