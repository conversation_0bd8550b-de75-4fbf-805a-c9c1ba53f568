package content

import (
	"strings"
	"time"

	"github.com/imdario/mergo"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

type Trophy struct {
	ObjectID    primitive.ObjectID `json:"-" bson:"_id,omitempty"`
	ID          string             `json:"_id,omitempty" bson:"-"`
	Name        string             `json:"name" bson:"name"`
	Identifier  string             `json:"identifier" bson:"identifier"`
	Requirement string             `json:"requirement" bson:"requirement"`
	Level       uint8              `json:"level" bson:"level"`
	Logo        string             `json:"logo" bson:"logo"`
	CreatedAt   time.Time          `json:"createdAt" bson:"createdAt"`
	UpdatedAt   time.Time          `json:"updatedAt" bson:"updatedAt"`
	Conquered   bool               `json:"conquered" bson:"conquered"`
}

func (t *Trophy) PrepareCreate() error {
	t.Name = strings.TrimSpace(t.Name)
	t.Identifier = strings.TrimSpace(strings.ToLower(t.Identifier))
	t.Requirement = strings.TrimSpace(t.Requirement)
	t.Logo = strings.TrimSpace(t.Logo)

	t.CreatedAt = time.Now()
	t.UpdatedAt = t.CreatedAt

	return t.ValidateCreate()
}

func (t *Trophy) ValidateCreate() error {
	if t.Name == "" {
		return ErrTrophyRequiredName
	}

	if t.Identifier == "" {
		return ErrTrophyRequiredIdentifier
	}

	if t.Requirement == "" {
		return ErrTrophyRequiredRequirement
	}

	return nil
}

func (t *Trophy) PrepareUpdate(newTrophy *Trophy) error {
	if err := mergo.Merge(t, newTrophy, mergo.WithOverride); err != nil {
		return err
	}

	t.UpdatedAt = time.Now()

	return t.ValidateUpdate()
}

func (t *Trophy) ValidateUpdate() error {

	if t.ID == "" {
		if !t.ObjectID.IsZero() {
			t.ID = t.ObjectID.Hex()
		} else {
			return ErrTrophyInvalidID
		}
	} else {
		trophyObjectId, err := primitive.ObjectIDFromHex(t.ID)
		if err != nil {
			return err
		}
		t.ObjectID = trophyObjectId
	}

	return t.ValidateCreate()
}

func (t *Trophy) Sanitize() *Trophy {
	t.ID = t.ObjectID.Hex()

	return t
}
