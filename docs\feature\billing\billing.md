# 1. Billing Models ( internal/model/billing/)
Plan Entity: Subscription tiers with pricing, features, permissions, and external provider IDs
Subscription Entity: Links users to plans with trial periods, grace periods, and auto-renewal
Payment Entity: Records all payment transactions with webhook data for auditing
Types & Helpers: Common constants, validation utilities, and helper functions

# 2. Repository Layer ( internal/repository/billing/)
MongoDB implementations for Plans, Subscriptions, and Payments
Proper indexing for performance optimization
Comprehensive CRUD operations with error handling
Webhook deduplication through unique indexes

# 3. Service Layer ( internal/service/billing/)
Business logic for subscription management
Trial period and grace period handling
Payment processing coordination
Webhook processing for Hotmart and Apple Pay
Access control methods for features and permissions

# 4. Controller Layer ( internal/controller/billing/)
REST API endpoints following v2 naming convention
Admin endpoints for plan management
User endpoints for subscription management
Webhook endpoints for payment providers
Comprehensive validation and error handling

# 5. Access Control Middleware ( internal/api/middlewares/subscription.go)
Subscription-based access control
Feature and permission checking
Trial period support
Grace period handling for cancelled subscriptions

# 6. API Integration
Fully integrated with repository, service, and controller registries
Follows existing codebase patterns and conventions
Ready to use with proper dependency injection

# 🔧 Key Features Implemented
12-month default subscription duration with 7-day free trial
Grace period handling: Cancelled subscriptions remain active until end date
Multiple active subscriptions per user with union of benefits
Webhook support for Hotmart and Apple Pay payment notifications
Comprehensive access control with features and permissions
Audit trail for all payment transactions
Auto-renewal functionality with configurable settings

# 🛡️ Security & Access Control
Role-based plan management: Only admins can create/modify plans
User-owned subscription management: Users can only access their own subscriptions
Webhook deduplication: Prevents duplicate payment processing
Comprehensive validation: All inputs validated at multiple layers

# 📋 Next Steps
Write comprehensive tests for all layers of the billing system
Set up webhook endpoints with proper authentication from payment providers
Configure payment provider credentials in your environment
Test the subscription flow end-to-end
Set up monitoring for payment processing and subscription lifecycle events