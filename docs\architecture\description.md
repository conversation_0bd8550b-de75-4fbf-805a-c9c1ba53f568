# Dinbora Backend Architecture Description

## **Language & Technology Stack**

**Primary Language:** Go (Golang)
- Clean architecture implementation with strong type system
- Built-in concurrency support for performance
- Standard library HTTP handling

**Database:** MongoDB (document-based NoSQL)
**Framework:** Echo (lightweight Go web framework)
- Middleware support for authentication, logging, CORS
- RESTful API routing

## **Architectural Pattern: Controller-Service-Repository**

The project follows a classic 3-layer architecture pattern:

### **1. Controller Layer** (`internal/controller/`)
**Purpose:** HTTP request handling and API endpoints
- Handles incoming HTTP requests
- Route registration and URL path mapping
- Input validation and request parsing
- Response formatting and error handling
- Authentication middleware integration

**Example:** Dashboard controller manages endpoints like:
- `GET /dashboard/financialmaps/me` - Get user's financial map
- `POST /dashboard/financialmaps/me/incomes` - Create income source
- `PUT /dashboard/financialmaps/me/investments/:id` - Update investment

### **2. Service Layer** (`internal/service/`)
**Purpose:** Business logic implementation and orchestration
- Implements core business rules and domain logic
- Coordinates operations between multiple repositories
- <PERSON>les data transformation and validation
- Manages transactions and external service integrations
- Cross-cutting concerns (logging, caching, etc.)

**Example:** Dashboard service handles:
- Financial map calculations and aggregations
- Income source management with validation
- Investment portfolio operations
- Net worth snapshot generation

### **3. Repository Layer** (`internal/repository/`)
**Purpose:** Data access abstraction and persistence
- Abstracts database operations from business logic
- Provides domain-oriented data access interface
- Handles CRUD operations and complex queries
- Manages MongoDB collections and document mapping
- Implements caching strategies

**Example:** Dashboard repository manages:
- Unified FinancialMap document operations
- Embedded document queries (income sources, investments, assets)
- Historical data retrieval for net worth tracking

## **What the Backend Does**

**Dinbora Backend** is a financial education and management API that provides:

### **Core Functionality:**

1. **Financial Management Dashboard**
   - Track income sources and monthly income
   - Manage strategic funds (emergency funds)
   - Investment portfolio tracking
   - Asset management (physical assets)
   - Net worth history and snapshots

2. **Educational Content System**
   - Trail-based learning paths for financial education
   - Lesson content delivery and progress tracking
   - Tutorial management and completion tracking
   - Achievement/trophy system for gamification

3. **User Management & Authentication**
   - Multi-provider authentication (Google, Apple, Facebook)
   - JWT-based authorization
   - User profiles and onboarding flows
   - Secure data vaulting

4. **Financial DNA System**
   - Family financial relationship tracking
   - Multi-generational financial insights
   - Investment probability analytics

5. **Dreamboard System**
   - Financial goal setting and visualization
   - Progress monitoring for financial objectives

6. **Billing & Subscriptions**
   - Stripe payment processing integration
   - Subscription management
   - Product catalog and pricing

## **Key Architectural Features**

### **Data Organization:**
- **Unified Document Design**: The dashboard uses a single `FinancialMap` document that embeds all related entities (income sources, investments, assets, snapshots)
- **Collection-based Storage**: Other features use dedicated MongoDB collections with proper indexing

### **Error Handling:**
- Standardized error types (Domain, REST, Validation, Internal)
- Centralized error handling middleware
- Structured error responses

### **Security:**
- JWT authentication with middleware guards
- Environment-based configuration for sensitive data
- CORS configuration for frontend integration
- Input validation and sanitization

### **Performance:**
- Connection pooling for database operations
- Caching layer implementation
- Concurrent initialization of services
- Graceful shutdown handling

### **External Integrations:**
- **Stripe API** for payment processing
- **SendGrid** for email notifications
- **OAuth providers** for authentication
- **MongoDB** for data persistence

## **Architecture Flow**

```mermaid
flowchart TD
    Client[Client Application] --> API[API Layer - Echo Framework]
    
    subgraph Backend[Backend Services]
        API --> Controllers[Controllers Layer]
        Controllers --> Services[Services Layer]
        Services --> Repositories[Repositories Layer]
        Repositories --> DataStores[Data Stores]
    end
    
    subgraph ExternalServices[External Services]
        Auth[Auth Providers - Google/Apple/Facebook]
        Payment[Stripe Payment Services]
        Email[SendGrid Email Services]
    end
    
    Controllers --> Auth
    Services --> Payment
    Services --> Email
    DataStores --> MongoDB[(MongoDB Database)]
```

## **Request Flow Example**

1. **Client Request**: Frontend sends HTTP request to API endpoint
2. **Controller**: Echo router directs request to appropriate controller
3. **Authentication**: Middleware validates JWT token and user permissions
4. **Service**: Controller calls service layer for business logic execution
5. **Repository**: Service layer calls repository for data operations
6. **Database**: Repository executes MongoDB queries
7. **Response**: Data flows back through layers to client as JSON

This architecture provides a scalable, maintainable backend that separates concerns clearly while supporting complex financial management and educational features through a well-structured REST API.
