name: Progression CI

on:
  push:
    branches: [ develop ]
    paths:
      - 'internal/model/progression/**'
      - 'internal/service/progression/**'
      - 'internal/repository/progression/**'
      - 'internal/controller/progression/**'
      - 'migration/trails/**'
      - 'migration/trails.extra/**'
      - '.github/workflows/progression-ci.yml'
  pull_request:
    branches: [ develop ]
    paths:
      - 'internal/model/progression/**'
      - 'internal/service/progression/**'
      - 'internal/repository/progression/**'
      - 'internal/controller/progression/**'
      - 'migration/trails/**'
      - 'migration/trails.extra/**'
      - '.github/workflows/progression-ci.yml'

permissions:
  contents: read
  actions: read
  checks: write
  security-events: write

jobs:
  lint:
    name: Lint
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Go
        uses: actions/setup-go@v4
        with:
          go-version: '1.22.7'
          cache: true

      - name: Install golangci-lint
        run: go install github.com/golangci/golangci-lint/cmd/golangci-lint@latest

      - name: Run golangci-lint
        run: golangci-lint run --timeout 5m ./internal/model/progression/... ./internal/service/progression/... ./internal/repository/progression/... ./internal/controller/progression/...

  test:
    name: Test
    runs-on: ubuntu-latest
    needs: lint
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Go
        uses: actions/setup-go@v4
        with:
          go-version: '1.22.7'
          cache: true

      - name: Install dependencies
        run: go mod download

      - name: Run tests with coverage
        run: go test -v -coverprofile=coverage.out ./internal/model/progression/... ./internal/service/progression/... ./internal/repository/progression/... ./internal/controller/progression/...

      - name: Generate coverage report
        run: go tool cover -html=coverage.out -o coverage.html

      - name: Upload coverage report
        uses: actions/upload-artifact@v4
        with:
          name: coverage-report
          path: coverage.html

  security:
    name: Security Scan
    runs-on: ubuntu-latest
    needs: lint
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Go
        uses: actions/setup-go@v4
        with:
          go-version: '1.22.7'
          cache: true

      - name: Install gosec
        run: go install github.com/securego/gosec/v2/cmd/gosec@latest

      - name: Run gosec
        run: gosec ./internal/model/progression/... ./internal/service/progression/... ./internal/repository/progression/... ./internal/controller/progression/...

  #build:
  #  name: Build
  #  runs-on: ubuntu-latest
  #  needs: [test, security]
  #  steps:
  #    - name: Checkout code
  #      uses: actions/checkout@v4
  #
  #    - name: Set up Go
  #      uses: actions/setup-go@v4
  #      with:
  #        go-version: '1.22.7'
  #        cache: true
  #
  #    - name: Build
  #      run: go build -v ./...

  notify:
    name: Notify
    runs-on: ubuntu-latest
    needs: [test, security]
    if: always()
    steps:
      - name: Notify success
        if: ${{ success() }}
        run: echo "All progression tests passed successfully!"

      - name: Notify failure
        if: ${{ failure() }}
        run: echo "Some progression tests failed. Please check the logs for details."
