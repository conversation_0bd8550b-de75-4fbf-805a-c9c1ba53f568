package dashboard

import (
	"strings"
	"time"

	"github.com/dsoplabs/dinbora-backend/internal/errors"
	"github.com/dsoplabs/dinbora-backend/internal/model/monetary"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// FinancialIndependence represents the financial independence metrics and calculations
type FinancialIndependence struct {
	ObjectID                  primitive.ObjectID         `json:"-" bson:"_id,omitempty"`
	ID                        string                     `json:"id,omitempty" bson:"-"`
	UserID                    string                     `json:"userID" bson:"userID"`
	RetirementTargetAmount    monetary.Amount            `json:"retirementTargetAmount" bson:"retirementTargetAmount"`
	MonthlyInvestment         monetary.Amount            `json:"monthlyInvestment" bson:"monthlyInvestment"`
	MonthlyExpenses           monetary.Amount            `json:"monthlyExpenses" bson:"monthlyExpenses"`
	CurrentNetWorth           monetary.Amount            `json:"currentNetWorth" bson:"currentNetWorth"`
	FinancialIndependenceData *FinancialIndependenceData `json:"financialIndependenceData" bson:"financialIndependenceData"`
	StrategicFund             *StrategicFund             `json:"strategicFund" bson:"strategicFund"`
	PartialIndependence       *PartialIndependence       `json:"partialIndependence" bson:"partialIndependence"`
	FullIndependence          *FullIndependence          `json:"fullIndependence" bson:"fullIndependence"`
	FirstTransactionDate      *time.Time                 `json:"firstTransactionDate" bson:"firstTransactionDate"`
	CreatedAt                 time.Time                  `json:"createdAt" bson:"createdAt"`
	UpdatedAt                 time.Time                  `json:"updatedAt" bson:"updatedAt"`
}

// FinancialIndependenceData represents the financial independence data
type FinancialIndependenceData struct {
	TargetDate          *time.Time      `json:"timeline" bson:"timeline"`
	TargetAmount        monetary.Amount `json:"targetAmount" bson:"targetAmount"`
	TargetPassiveIncome monetary.Amount `json:"targetPassiveIncome" bson:"targetPassiveIncome"`
}

// PartialIndependence represents the partial independence metrics
type PartialIndependence struct {
	CurrentValue monetary.Amount `json:"currentValue" bson:"currentValue"`
	GoalValue    monetary.Amount `json:"goalValue" bson:"goalValue"`
}

// FullIndependence represents the full independence metrics
type FullIndependence struct {
	CurrentValue monetary.Amount `json:"currentValue" bson:"currentValue"`
	GoalValue    monetary.Amount `json:"goalValue" bson:"goalValue"`
}

// PrepareCreate prepares the FinancialIndependence for creation
func (fi *FinancialIndependence) PrepareCreate() error {
	fi.UserID = strings.TrimSpace(fi.UserID)
	if fi.UserID == "" {
		return errors.New(errors.Model, "userID is required", errors.Validation, nil)
	}

	now := time.Now()
	fi.CreatedAt = now
	fi.UpdatedAt = now

	return nil
}

// PrepareUpdate prepares the FinancialIndependence for update
func (fi *FinancialIndependence) PrepareUpdate() error {
	fi.UserID = strings.TrimSpace(fi.UserID)
	if fi.UserID == "" {
		return errors.New(errors.Model, "userID is required", errors.Validation, nil)
	}

	fi.UpdatedAt = time.Now()

	return nil
}
