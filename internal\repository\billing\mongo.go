package billing

import (
	"go.mongodb.org/mongo-driver/mongo"
)

// mongoDB implements the main billing repository interface
type mongoDB struct {
	plans         PlanRepository
	subscriptions SubscriptionRepository
	payments      PaymentRepository
}

// New creates a new billing repository with MongoDB implementations
func New(db *mongo.Database) Repository {
	return &mongoDB{
		plans:         NewPlanRepository(db),
		subscriptions: NewSubscriptionRepository(db),
		payments:      NewPaymentRepository(db),
	}
}

// Plans returns the plan repository
func (r *mongoDB) Plans() PlanRepository {
	return r.plans
}

// Subscriptions returns the subscription repository
func (r *mongoDB) Subscriptions() SubscriptionRepository {
	return r.subscriptions
}

// Payments returns the payment repository
func (r *mongoDB) Payments() PaymentRepository {
	return r.payments
}
