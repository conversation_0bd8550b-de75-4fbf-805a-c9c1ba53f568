package dreamboard

import (
	"github.com/dsoplabs/dinbora-backend/internal/model/dreamboard"
	"github.com/dsoplabs/dinbora-backend/internal/model/monetary"
)

// Request DTOs

// Implement the Requests later
// CreateDreamRequest represents the request to create a dream
type CreateDreamRequest struct {
	Category          string          `json:"category" validate:"required"`
	Title             string          `json:"title" validate:"required,min=1,max=100"`
	TimeFrame         string          `json:"timeFrame" validate:"required"`
	Deadline          string          `json:"deadline" validate:"required"`
	EstimatedCost     monetary.Amount `json:"estimatedCost" validate:"required,min=1"`
	MonthlySavings    monetary.Amount `json:"monthlySavings" validate:"required,min=1"`
	MoneySource       []string        `json:"moneySource" validate:"required"`
	CustomMoneySource string          `json:"customMoneySource,omitempty"`
	IsShared          bool            `json:"isShared"`
}

// UpdateDreamRequest represents the request to update a dream
type UpdateDreamRequest struct {
	Category          string          `json:"category"`
	Title             string          `json:"title" validate:"min=1,max=100"`
	TimeFrame         string          `json:"timeFrame"`
	Deadline          string          `json:"deadline"`
	EstimatedCost     monetary.Amount `json:"estimatedCost" validate:"min=1"`
	MonthlySavings    monetary.Amount `json:"monthlySavings" validate:"min=1"`
	MoneySource       []string        `json:"moneySource"`
	CustomMoneySource string          `json:"customMoneySource,omitempty"`
	Completed         *bool           `json:"completed,omitempty"`
}

// CreateCategoryRequest represents the request to create a category
type CreateCategoryRequest struct {
	Identifier string `json:"identifier" validate:"required"`
	Name       string `json:"name" validate:"required"`
	Icon       string `json:"icon" validate:"required"`
	Color      string `json:"color" validate:"required"`
}

// UpdateCategoryRequest represents the request to update a category
type UpdateCategoryRequest struct {
	Identifier string `json:"identifier"`
	Name       string `json:"name"`
	Icon       string `json:"icon"`
	Color      string `json:"color"`
}

// JoinSharedDreamRequest represents the request to join a shared dream
type JoinSharedDreamRequest struct {
	Code                 string          `json:"code" validate:"required"`
	MonthlyPledgedAmount monetary.Amount `json:"monthlyPledgedAmount" validate:"required,min=1"`
}

// UpdateContributionRequest represents the request to update a contribution
type UpdateContributionRequest struct {
	MonthlyPledgedAmount monetary.Amount               `json:"monthlyPledgedAmount" validate:"min=1"`
	Status               dreamboard.ContributionStatus `json:"status"`
}

// UpdateShareLinkStatusRequest represents the request to update share link status
type UpdateShareLinkStatusRequest struct {
	IsEnabled bool `json:"isEnabled"`
}

// UpdateContributionStatusRequest represents the request to update contribution status
type UpdateContributionStatusRequest struct {
	Status dreamboard.ContributionStatus `json:"status" validate:"required"`
}

// Response DTOs

// DreamboardResponse represents the response for dreamboard operations
type DreamboardResponse struct {
	*dreamboard.Dreamboard
	// Additional computed fields can be added here if needed
}

// DreamResponse represents the response for dream operations
type DreamResponse struct {
	*dreamboard.Dream
	// Additional computed fields can be added here if needed
}

// CategoryResponse represents the response for category operations
type CategoryResponse struct {
	*dreamboard.Category
	// Additional computed fields can be added here if needed
}

// ContributionResponse represents the response for contribution operations
type ContributionResponse struct {
	*dreamboard.Contribution
	// Additional computed fields can be added here if needed
}

// ShareLinkResponse represents the response for share link operations
type ShareLinkResponse struct {
	*dreamboard.ShareLink
	ShareURL string `json:"shareUrl"`
}

// DreamListResponse represents the response for dream list operations
type DreamListResponse struct {
	Dreams []*dreamboard.Dream `json:"dreams"`
	Total  int                 `json:"total"`
}

// ContributionListResponse represents the response for contribution list operations
type ContributionListResponse struct {
	Contributions []*dreamboard.Contribution `json:"contributions"`
	Total         int                        `json:"total"`
}

// Error Response DTOs

// ErrorResponse represents a standard error response
type ErrorResponse struct {
	Error   string `json:"error"`
	Message string `json:"message"`
	Code    int    `json:"code"`
}

// ValidationErrorResponse represents a validation error response
type ValidationErrorResponse struct {
	Error   string            `json:"error"`
	Message string            `json:"message"`
	Code    int               `json:"code"`
	Fields  map[string]string `json:"fields,omitempty"`
}

// Success Response DTOs

// SuccessResponse represents a standard success response
type SuccessResponse struct {
	Success bool        `json:"success"`
	Message string      `json:"message"`
	Data    interface{} `json:"data,omitempty"`
}

// CreatedResponse represents a resource creation response
type CreatedResponse struct {
	Success bool        `json:"success"`
	Message string      `json:"message"`
	ID      string      `json:"id"`
	Data    interface{} `json:"data,omitempty"`
}

// Helper functions for response creation

// NewDreamboardResponse creates a new dreamboard response
func NewDreamboardResponse(dreamboard *dreamboard.Dreamboard) *DreamboardResponse {
	return &DreamboardResponse{
		Dreamboard: dreamboard,
	}
}

// NewDreamResponse creates a new dream response
func NewDreamResponse(dream *dreamboard.Dream) *DreamResponse {
	return &DreamResponse{
		Dream: dream,
	}
}

// NewCategoryResponse creates a new category response
func NewCategoryResponse(category *dreamboard.Category) *CategoryResponse {
	return &CategoryResponse{
		Category: category,
	}
}

// NewContributionResponse creates a new contribution response
func NewContributionResponse(contribution *dreamboard.Contribution) *ContributionResponse {
	return &ContributionResponse{
		Contribution: contribution,
	}
}

// NewShareLinkResponse creates a new share link response with URL
func NewShareLinkResponse(shareLink *dreamboard.ShareLink, shareURL string) *ShareLinkResponse {
	return &ShareLinkResponse{
		ShareLink: shareLink,
		ShareURL:  shareURL,
	}
}

// NewDreamListResponse creates a new dream list response
func NewDreamListResponse(dreams []*dreamboard.Dream) *DreamListResponse {
	return &DreamListResponse{
		Dreams: dreams,
		Total:  len(dreams),
	}
}

// NewContributionListResponse creates a new contribution list response
func NewContributionListResponse(contributions []*dreamboard.Contribution) *ContributionListResponse {
	return &ContributionListResponse{
		Contributions: contributions,
		Total:         len(contributions),
	}
}

// NewSuccessResponse creates a new success response
func NewSuccessResponse(message string, data interface{}) *SuccessResponse {
	return &SuccessResponse{
		Success: true,
		Message: message,
		Data:    data,
	}
}

// NewCreatedResponse creates a new created response
func NewCreatedResponse(message string, id string, data interface{}) *CreatedResponse {
	return &CreatedResponse{
		Success: true,
		Message: message,
		ID:      id,
		Data:    data,
	}
}
