package vault

import (
	"context"
	"log"

	"github.com/dsoplabs/dinbora-backend/internal/errors"
	"github.com/dsoplabs/dinbora-backend/internal/model"
	"github.com/dsoplabs/dinbora-backend/internal/repository"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

type mongoDB struct {
	collection *mongo.Collection
}

func New(db *mongo.Database) Repository {
	repo := &mongoDB{
		collection: db.Collection(repository.VAULTS_COLLECTION),
	}

	// Create unique index on user field
	_, err := repo.collection.Indexes().CreateOne(
		context.Background(),
		mongo.IndexModel{
			Keys:    bson.D{{Key: "user", Value: 1}},
			Options: options.Index().SetUnique(true).SetName("user"),
		},
	)
	if err != nil {
		// Log error but don't fail - index might already exist
		log.Println("warning: failed to create index on vault.identifier field")
		db.Client().Disconnect(context.Background())
	}

	return repo
}

func (m mongoDB) Create(ctx context.Context, vault *model.Vault) error {
	_, err := m.collection.InsertOne(ctx, vault)
	if err != nil {
		if mongo.IsDuplicateKeyError(err) {
			return errors.New(errors.Repository, "vault already exists", errors.Conflict, err)
		}
		return errors.New(errors.Repository, "database error", errors.Internal, err)
	}
	return nil
}

func (m mongoDB) Find(ctx context.Context, id string) (*model.Vault, error) {
	objectId, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return nil, errors.New(errors.Repository, "invalid vault id format", errors.Validation, err)
	}

	var vault model.Vault
	if err = m.collection.FindOne(ctx, bson.D{{Key: "_id", Value: objectId}}).Decode(&vault); err != nil {
		if err == mongo.ErrNoDocuments {
			return nil, errors.New(errors.Repository, "vault not found", errors.NotFound, err)
		}
		return nil, errors.New(errors.Repository, "database error", errors.Internal, err)
	}
	return &vault, nil
}

func (m mongoDB) FindByUser(ctx context.Context, userId string) (*model.Vault, error) {
	var vault model.Vault
	if err := m.collection.FindOne(ctx, bson.D{{Key: "user", Value: userId}}).Decode(&vault); err != nil {
		if err == mongo.ErrNoDocuments {
			return nil, errors.New(errors.Repository, "vault not found", errors.NotFound, err)
		}
		return nil, errors.New(errors.Repository, "database error", errors.Internal, err)
	}
	return &vault, nil
}

func (m mongoDB) Update(ctx context.Context, vault *model.Vault) error {
	result, err := m.collection.UpdateOne(ctx,
		bson.D{primitive.E{Key: "_id", Value: vault.ObjectID}},
		primitive.M{"$set": vault})
	if err != nil {
		if mongo.IsDuplicateKeyError(err) {
			return errors.New(errors.Repository, "vault already exists", errors.Conflict, err)
		}
		return errors.New(errors.Repository, "database error", errors.Internal, err)
	}

	if result.MatchedCount <= 0 {
		return errors.New(errors.Repository, "vault not found", errors.NotFound, nil)
	}
	return nil
}

func (m mongoDB) Delete(ctx context.Context, id string) error {
	objectID, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return errors.New(errors.Repository, "invalid vault id format", errors.Validation, err)
	}

	result, err := m.collection.DeleteOne(ctx, bson.D{{Key: "_id", Value: objectID}})
	if err != nil {
		return errors.New(errors.Repository, "database error", errors.Internal, err)
	}

	if result.DeletedCount <= 0 {
		return errors.New(errors.Repository, "vault not found", errors.NotFound, nil)
	}
	return nil
}
