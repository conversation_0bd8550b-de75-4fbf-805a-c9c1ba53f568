package repository

import (
	"sync"

	"github.com/dsoplabs/dinbora-backend/internal/repository/billing"
	"github.com/dsoplabs/dinbora-backend/internal/repository/content/investmentcategory"
	"github.com/dsoplabs/dinbora-backend/internal/repository/content/ticker"
	"github.com/dsoplabs/dinbora-backend/internal/repository/content/trail"
	"github.com/dsoplabs/dinbora-backend/internal/repository/content/trophy"
	"github.com/dsoplabs/dinbora-backend/internal/repository/content/wallet"
	"github.com/dsoplabs/dinbora-backend/internal/repository/dashboard"
	"github.com/dsoplabs/dinbora-backend/internal/repository/dreamboard"
	"github.com/dsoplabs/dinbora-backend/internal/repository/financialdna"
	"github.com/dsoplabs/dinbora-backend/internal/repository/financialsheet"
	"github.com/dsoplabs/dinbora-backend/internal/repository/league" // Added league repository import
	"github.com/dsoplabs/dinbora-backend/internal/repository/progression"
	"github.com/dsoplabs/dinbora-backend/internal/repository/user"
	"github.com/dsoplabs/dinbora-backend/internal/repository/vault"
	"go.mongodb.org/mongo-driver/mongo"
)

type RepositoryRegistry struct {
	// Billing
	Billing billing.Repository

	// Content
	Investimentcategory investmentcategory.Repository
	Ticker              ticker.Repository
	Trail               trail.Repository
	Trophy              trophy.Repository
	Wallet              wallet.Repository

	Dashboard  dashboard.Repository
	Dreamboard dreamboard.Repository

	FinancialDNA   financialdna.Repository
	FinancialSheet financialsheet.Repository
	League         league.Repository // Added League repository

	Progression progression.Repository

	User  user.Repository
	Vault vault.Repository
}

type RepositoryContainer struct {
	repositories map[string]interface{}
	db           *mongo.Database
	mu           sync.RWMutex
}

func NewContainer(db *mongo.Database) *RepositoryContainer {
	return &RepositoryContainer{
		repositories: make(map[string]interface{}),
		db:           db,
	}
}

func (rc *RepositoryContainer) Register(name string, repo interface{}) {
	rc.mu.Lock()
	defer rc.mu.Unlock()
	rc.repositories[name] = repo
}

func (rc *RepositoryContainer) Get(name string) interface{} {
	rc.mu.RLock()
	defer rc.mu.RUnlock()
	return rc.repositories[name]
}

func (rc *RepositoryContainer) Initialize() *RepositoryRegistry {
	// Lazy initialization of repositories
	lazyInitRepo := func(name string, initFunc func() interface{}) interface{} {
		if existing := rc.Get(name); existing != nil {
			return existing
		}
		repo := initFunc()
		rc.Register(name, repo)
		return repo
	}

	// Initialize all repositories with the database
	// Billing
	billingRepo := lazyInitRepo("billing", func() interface{} {
		return billing.New(rc.db)
	}).(billing.Repository)

	// Content
	investmentCategoryRepo := lazyInitRepo("investmentcategory", func() interface{} {
		return investmentcategory.New(rc.db)
	}).(investmentcategory.Repository)
	tickerRepo := lazyInitRepo("ticker", func() interface{} {
		return ticker.New(rc.db)
	}).(ticker.Repository)
	trailRepo := lazyInitRepo("trail", func() interface{} {
		return trail.New(rc.db)
	}).(trail.Repository)
	trophyRepo := lazyInitRepo("trophy", func() interface{} {
		return trophy.New(rc.db)
	}).(trophy.Repository)
	walletRepo := lazyInitRepo("wallet", func() interface{} {
		return wallet.New(rc.db)
	}).(wallet.Repository)

	dashboardRepo := lazyInitRepo("dashboard", func() interface{} {
		return dashboard.New(rc.db)
	}).(dashboard.Repository)
	dreamboardRepo := lazyInitRepo("dreamboard", func() interface{} {
		return dreamboard.New(rc.db)
	}).(dreamboard.Repository)

	financialDNARepo := lazyInitRepo("financialdna", func() interface{} {
		return financialdna.New(rc.db)
	}).(financialdna.Repository)

	financialSheetRepo := lazyInitRepo("financialsheet", func() interface{} {
		return financialsheet.New(rc.db)
	}).(financialsheet.Repository)

	progressionRepo := lazyInitRepo("progression", func() interface{} {
		return progression.New(rc.db)
	}).(progression.Repository)

	userRepo := lazyInitRepo("user", func() interface{} {
		return user.New(rc.db)
	}).(user.Repository)
	vaultRepo := lazyInitRepo("vault", func() interface{} {
		return vault.New(rc.db)
	}).(vault.Repository)
	leagueRepo := lazyInitRepo("league", func() interface{} {
		return league.New(rc.db) // Corrected constructor to package.New
	}).(league.Repository)

	return &RepositoryRegistry{
		Billing:             billingRepo,
		Investimentcategory: investmentCategoryRepo,
		Ticker:              tickerRepo,
		Trail:               trailRepo,
		Trophy:              trophyRepo,
		Wallet:              walletRepo,
		Dashboard:           dashboardRepo,
		Dreamboard:          dreamboardRepo,
		FinancialDNA:        financialDNARepo,
		FinancialSheet:      financialSheetRepo,
		Progression:         progressionRepo,
		User:                userRepo,
		Vault:               vaultRepo,
		League:              leagueRepo, // Added League repository to registry
	}
}
