package user

import (
	"net/http"

	"github.com/dsoplabs/dinbora-backend/internal/api/middlewares"
	"github.com/dsoplabs/dinbora-backend/internal/model"
	"github.com/labstack/echo/v4"
)

// FindEmployeesByHR returns all employees that were referred by the HR user
// This endpoint is protected by HR role middleware and returns a list of users
// where ReferringUserID matches the requesting HR user's ID
func (uc *controller) FindEmployeesByHR() echo.HandlerFunc {
	return func(c echo.Context) error {
		ctx := c.Request().Context()

		// Get user context from middleware (set by UserContextMiddleware)
		userCtx, err := middlewares.GetUserContext(c)
		if err != nil {
			return err
		}

		// Call service to get employees for this HR user
		employees, err := uc.Service.FindEmployeesByHR(ctx, userCtx.UserID, userCtx.Roles)
		if err != nil {
			return err
		}

		// Sanitize user data before returning (remove sensitive information)
		var sanitizedEmployees []*model.User
		for _, employee := range employees {
			sanitizedEmployees = append(sanitizedEmployees, employee.Sanitize())
		}

		return c.JSON(http.StatusOK, sanitizedEmployees)
	}
}
