package aiassistant

import (
	"context"
	"time"

	"github.com/dsoplabs/dinbora-backend/internal/model/aiassistant"
)

// fetchProfileData retrieves and formats user profile data
func (s *service) fetchProfileData(ctx context.Context, userID string) (aiassistant.ProfileSummaryDTO, error) {
	user, err := s.UserService.Find(ctx, userID)
	if err != nil {
		return aiassistant.ProfileSummaryDTO{}, err
	}

	profileDTO := aiassistant.ProfileSummaryDTO{
		Name:  user.Name,
		Email: user.Email,
	}

	// Add onboarding data if available
	if user.Onboarding != nil {
		profileDTO.AgeRange = user.Onboarding.AgeRange.LabelFor("pt")
		profileDTO.FinancialSituation = user.Onboarding.FinancialSituation.LabelFor("pt")
		profileDTO.FinancialGoal = user.Onboarding.FinancialGoal.LabelFor("pt")

		// Add personal interests
		interests := make([]string, 0, len(user.Onboarding.PersonalInterests))
		for _, interest := range user.Onboarding.PersonalInterests {
			interests = append(interests, interest.LabelFor("pt"))
		}
		profileDTO.PersonalInterests = interests

		// Check if onboarding is complete
		profileDTO.OnboardingComplete = !user.Onboarding.CompletedAt.IsZero()
	}

	return profileDTO, nil
}

// fetchDSOPProgressData retrieves and formats educational progress data
func (s *service) fetchDSOPProgressData(ctx context.Context, userID string) (aiassistant.DSOPProgressDTO, error) {
	userProgression, err := s.ProgressionService.FindByUser(ctx, userID)
	if err != nil {
		return aiassistant.DSOPProgressDTO{}, err
	}

	// Get all trophies for counting
	trophies, err := s.ProgressionService.FindAllTrophies(ctx, userID)
	if err != nil {
		return aiassistant.DSOPProgressDTO{}, err
	}

	// Get all trophy content for total count
	allTrophyContent, err := s.ProgressionService.FindAllTrophiesContent(ctx, userID)
	if err != nil {
		return aiassistant.DSOPProgressDTO{}, err
	}

	// Initialize counters
	completedLessons := 0
	totalLessons := 0
	completedChallenges := 0
	totalChallenges := 0
	currentTrails := make([]string, 0)
	completedTrails := make([]string, 0)

	// Process trail data
	for _, trail := range userProgression.Trails {
		// Count lessons
		for _, lesson := range trail.Lessons {
			totalLessons++
			if lesson.Completed {
				completedLessons++
			}
		}

		// Count challenges
		if trail.Challenge != nil {
			totalChallenges++
			if trail.Challenge.Completed {
				completedChallenges++
			}
		}

		// Get trail content to get the name
		trailContent, err := s.TrailService.Find(ctx, trail.ID)
		if err != nil {
			// Log error but continue with other trails
			continue
		}
		trailName := trailContent.Name

		// Categorize trail as current or completed
		if trail.LessonsCompleted && (trail.Challenge == nil || trail.ChallengeCompleted) {
			completedTrails = append(completedTrails, trailName)
		} else if len(trail.Lessons) > 0 {
			currentTrails = append(currentTrails, trailName)
		}
	}

	// Collect recent lesson topics and challenge types
	recentLessonTopics := make([]string, 0)
	recentChallengeTypes := make([]string, 0)

	// Helper function to check if a string is in a slice
	contains := func(slice []string, item string) bool {
		for _, s := range slice {
			if s == item {
				return true
			}
		}
		return false
	}

	// Process recent completed lessons and challenges (last 5)
	for _, trail := range userProgression.Trails {
		// Get completed lessons
		for _, lesson := range trail.Lessons {
			if lesson.Completed && len(recentLessonTopics) < 5 {
				// Add lesson identifier if not already in the list
				if lesson.Identifier != "" && !contains(recentLessonTopics, lesson.Identifier) {
					recentLessonTopics = append(recentLessonTopics, lesson.Identifier)
				}
			}
		}

		// Get completed challenges
		if trail.Challenge != nil && trail.Challenge.Completed && len(recentChallengeTypes) < 5 {
			// Add challenge identifier if not already in the list
			if trail.Challenge.Identifier != "" && !contains(recentChallengeTypes, trail.Challenge.Identifier) {
				recentChallengeTypes = append(recentChallengeTypes, trail.Challenge.Identifier)
			}
		}
	}

	// Create the DTO
	dsopDTO := aiassistant.DSOPProgressDTO{
		CompletedLessons:     completedLessons,
		TotalLessons:         totalLessons,
		CompletedChallenges:  completedChallenges,
		TotalChallenges:      totalChallenges,
		CurrentTrails:        currentTrails,
		CompletedTrails:      completedTrails,
		EarnedTrophies:       len(trophies),
		TotalTrophies:        len(allTrophyContent),
		RecentLessonTopics:   recentLessonTopics,
		RecentChallengeTypes: recentChallengeTypes,
	}

	return dsopDTO, nil
}

// fetchActiveDreamsData retrieves and formats active dreams data
func (s *service) fetchActiveDreamsData(ctx context.Context, userID string) ([]aiassistant.DreamForAIDTO, error) {
	dreamboard, err := s.DreamboardService.FindByUser(ctx, userID)
	if err != nil {
		return nil, err
	}

	// Process dreams
	dreamDTOs := make([]aiassistant.DreamForAIDTO, 0, len(dreamboard.Dreams))
	for _, dream := range dreamboard.Dreams {
		// Skip completed dreams
		if dream.Completed {
			continue
		}

		// Find category name
		categoryName := "Outros"
		for _, category := range dreamboard.Categories {
			if category.Identifier == string(dream.Category) {
				categoryName = category.Name
				break
			}
		}

		// Calculate percent saved
		percentSaved := 0.0
		if dream.EstimatedCost > 0 {
			percentSaved = float64(dream.MonthlySavings) / float64(dream.EstimatedCost) * 100
		}

		// Calculate months remaining
		monthsRemaining := 0
		// Use deadline instead of TargetDate
		now := time.Now()
		diff := dream.Deadline.Sub(now)
		monthsRemaining = int(diff.Hours() / 24 / 30)
		if monthsRemaining < 0 {
			monthsRemaining = 0
		}

		// Create dream DTO
		dreamDTO := aiassistant.DreamForAIDTO{
			Name:            dream.Title,
			Category:        categoryName,
			TargetAmount:    int64(dream.EstimatedCost),
			SavedAmount:     int64(dream.MonthlySavings),
			PercentSaved:    percentSaved,
			MonthlyNeeded:   int64(dream.MonthlySavings),
			Priority:        0, // No priority field in the model
			MonthsRemaining: monthsRemaining,
		}

		// Add target date
		dreamDTO.TargetDate = dream.Deadline.Format("2006-01-02")

		dreamDTOs = append(dreamDTOs, dreamDTO)
	}

	return dreamDTOs, nil
}

// fetchFinancialDNAData retrieves and formats financial DNA data
func (s *service) fetchFinancialDNAData(ctx context.Context, userID string) (aiassistant.FinancialDNASnapshotDTO, error) {
	financialDNA, err := s.FinancialDNAService.FindByUser(ctx, userID)
	if err != nil {
		return aiassistant.FinancialDNASnapshotDTO{}, err
	}

	// Create the DTO
	dnaDTO := aiassistant.FinancialDNASnapshotDTO{
		TreeProgress:          float64(financialDNA.TreeProgress.Progress),
		FinancialDistribution: make(map[string]float64),
		BreakCycles:           []string{string(financialDNA.BreakCycles.Category)},
	}

	// Convert financial distribution
	dnaDTO.FinancialDistribution["investor"] = float64(financialDNA.FinancialDistribution.Investor)
	dnaDTO.FinancialDistribution["balanced"] = float64(financialDNA.FinancialDistribution.Balanced)
	dnaDTO.FinancialDistribution["indebted"] = float64(financialDNA.FinancialDistribution.Indebted)
	dnaDTO.FinancialDistribution["overindebted"] = float64(financialDNA.FinancialDistribution.Overindeb) // Map Overindeb to overindebted

	// Process key family members
	keyMembers := make([]aiassistant.FamilyMemberSummaryDTO, 0, len(financialDNA.Members))
	for _, member := range financialDNA.Members {
		// Determine relationship
		relationship := "Outro"
		if len(member.ParentIDs) == 0 && len(member.ChildrenIDs) > 0 {
			relationship = "Pai/Mãe"
		} else if len(member.ParentIDs) > 0 && len(member.ChildrenIDs) == 0 {
			relationship = "Filho(a)"
		} else if len(member.ParentIDs) > 0 && len(member.ChildrenIDs) > 0 {
			relationship = "Familiar"
		}

		// Create member DTO
		memberDTO := aiassistant.FamilyMemberSummaryDTO{
			Name:            member.Name,
			Relationship:    relationship,
			FinancialStatus: string(member.FinancialStatus),
		}

		keyMembers = append(keyMembers, memberDTO)
	}

	dnaDTO.KeyMembers = keyMembers

	return dnaDTO, nil
}
