package trophy

import (
	"context"

	"github.com/dsoplabs/dinbora-backend/internal/errors"
	"github.com/dsoplabs/dinbora-backend/internal/model/content"
	"github.com/dsoplabs/dinbora-backend/internal/repository/content/trophy"
)

type Service interface {
	// Trophy CRUD
	Create(ctx context.Context, trophy *content.Trophy) error
	Find(ctx context.Context, id string) (*content.Trophy, error)
	FindAll(ctx context.Context) ([]*content.Trophy, error)
	FindByIdentifier(ctx context.Context, identifier string) (*content.Trophy, error)
	FindByRequirement(ctx context.Context, requirement string) (*content.Trophy, error)
	Update(ctx context.Context, trophy *content.Trophy) error
	Delete(ctx context.Context, id string) error
}

type service struct {
	Repository trophy.Repository
}

func New(repository trophy.Repository) Service {
	return &service{
		Repository: repository,
	}
}

// CRUD
func (s *service) Create(ctx context.Context, trophy *content.Trophy) error {
	foundTrophy, err := s.Repository.FindByIdentifier(ctx, trophy.Identifier)
	if err == nil && foundTrophy != nil {
		return errors.New(errors.Service, "trophy already exists", errors.Conflict, err)
	}

	if err = s.Repository.Create(ctx, trophy); err != nil {
		return err
	}

	return nil
}

func (s *service) Find(ctx context.Context, id string) (*content.Trophy, error) {
	foundTrophy, err := s.Repository.Find(ctx, id)
	if err != nil {
		return nil, err
	}
	foundTrophy.ID = foundTrophy.ObjectID.Hex()
	return foundTrophy, nil
}

func (s *service) FindAll(ctx context.Context) ([]*content.Trophy, error) {
	foundTrophies, err := s.Repository.FindAll(ctx)
	if err != nil {
		return nil, err
	}
	for _, foundTrophy := range foundTrophies {
		foundTrophy.ID = foundTrophy.ObjectID.Hex()
	}

	return foundTrophies, nil
}

func (s *service) FindByIdentifier(ctx context.Context, identifier string) (*content.Trophy, error) {
	foundTrophy, err := s.Repository.FindByIdentifier(ctx, identifier)
	if err != nil {
		return nil, err
	}
	foundTrophy.ID = foundTrophy.ObjectID.Hex()
	return foundTrophy, nil
}

func (s *service) FindByRequirement(ctx context.Context, requirement string) (*content.Trophy, error) {
	foundTrophy, err := s.Repository.FindByRequirement(ctx, requirement)
	if err != nil {
		return nil, err
	}
	foundTrophy.ID = foundTrophy.ObjectID.Hex()
	return foundTrophy, nil
}

func (s *service) Update(ctx context.Context, trophy *content.Trophy) error {
	return s.Repository.Update(ctx, trophy)
}

func (s *service) Delete(ctx context.Context, id string) error {
	return s.Repository.Delete(ctx, id)
}
