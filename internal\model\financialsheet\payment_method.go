package financialsheet

import "github.com/dsoplabs/dinbora-backend/internal/errors"

// PaymentMethodInfo represents the JSON structure for payment method information
type PaymentMethodInfo struct {
	Identifier string       `json:"identifier"`
	Name       string       `json:"name"`
	Icon       CategoryIcon `json:"icon"`
}

type PaymentMethod byte

const (
	PaymentMethodUndefined PaymentMethod = iota

	PaymentMethodOpt1
	PaymentMethodOpt2
	PaymentMethodOpt3
	PaymentMethodOpt4
	PaymentMethodOpt5

	PaymentMethodOther
)

// IsValid validates the payment method value
func (pm PaymentMethod) IsValid() bool {
	return pm >= PaymentMethodOpt1 && pm <= PaymentMethodOther
}

func (pm PaymentMethod) Validate() error {
	if !pm.IsValid() {
		return errors.New(errors.Model, "invalid payment method value", errors.Validation, nil)
	}
	return nil
}

// GetPaymentMethodInfo belongs to the Category it returns the PaymentMethodInfo representation of a PaymentMethod based on the category context
func (c Category) GetPaymentMethodInfo(pm PaymentMethod) PaymentMethodInfo {
	switch c.Type {
	case CategoryTypeIncome:
		switch pm {
		case PaymentMethodOpt1:
			return PaymentMethodInfo{c.Identifier.String() + "_bank_transfer", "Transferência Bancária", CategoryIconIncomeBankTransfer}
		case PaymentMethodOpt2:
			return PaymentMethodInfo{c.Identifier.String() + "_cash", "Dinheiro", CategoryIconIncomeCash}
		case PaymentMethodOpt3:
			return PaymentMethodInfo{c.Identifier.String() + "_pix", "PIX", CategoryIconIncomePix}
		case PaymentMethodOpt4:
			return PaymentMethodInfo{c.Identifier.String() + "_credit_card", "Cartão de Crédito", CategoryIconIncomeCreditCard}
		case PaymentMethodOpt5:
			return PaymentMethodInfo{c.Identifier.String() + "_check", "Cheque", CategoryIconIncomeCheck}
		case PaymentMethodOther:
			return PaymentMethodInfo{"other", "Outros", CategoryIconOther}
		default:
			return PaymentMethodInfo{"undefined", "Indefinido", CategoryIconUndefined}
		}
	case CategoryTypeCostsOfLiving, CategoryTypeExpense:
		switch pm {
		case PaymentMethodOpt1:
			return PaymentMethodInfo{c.Identifier.String() + "_cash", "Dinheiro", CategoryIconExpenseCash}
		case PaymentMethodOpt2:
			return PaymentMethodInfo{c.Identifier.String() + "_pix", "PIX", CategoryIconExpensePix}
		case PaymentMethodOpt3:
			return PaymentMethodInfo{c.Identifier.String() + "_debit_card", "Cartão de Débito", CategoryIconExpenseDebitCard}
		case PaymentMethodOpt4:
			return PaymentMethodInfo{c.Identifier.String() + "_credit_card", "Cartão de Crédito", CategoryIconExpenseCreditCard}
		case PaymentMethodOpt5:
			return PaymentMethodInfo{c.Identifier.String() + "_bank_slip", "Boleto", CategoryIconExpenseBankSlip}
		case PaymentMethodOther:
			return PaymentMethodInfo{"other", "Outros", CategoryIconOther}
		default:
			return PaymentMethodInfo{"undefined", "Indefinido", CategoryIconUndefined}
		}
	default:
		return PaymentMethodInfo{"undefined", "Indefinido", CategoryIconUndefined}
	}
}
