I need to implement a billing system, model, controller, service and repository layer. The billing will contain a plan, subscription, payment.

Plan defines a subscription tier available for purchase.
Subscription links a User to a Plan. This is the source of truth for access control.
Payment records a single transaction event from a payment provider. (Log every transaction event here for auditing and support.)

The subscription will have 12 month long with automatic reneaw. Users can test it for 7 days. A user can have multiple subscriptions. When user cancelled a subscription after 7 days don't immediatly cut the user access but wait for the endperiod.

I will use hotmart and applepay as payment providers. And will deal with the payment through webhooks.

A middleware will control which features users can access depending on their sunscroption.

# Strutuctured

I need to implement a comprehensive billing system following the clean architecture pattern used in this codebase (model/repository/service/controller layers). The system should include three core entities:

**Plan Entity:**
- Defines subscription tiers available for purchase
- Should include fields like: ID, name, price (using monetary.Amount type), currency, features/permissions, status (active/inactive), HotmartProductID, AppleProductID.

**Subscription Entity:**
- Links a User to a Plan and serves as the source of truth for access control
- Should include fields like: ID, userID, planID, status (active/trial/cancelled/expired), provider (hotmart/applepay), startDate, endDate, trialEndDate, autoRenew boolean, createdAt, updatedAt
- Default subscription duration: 12 months with automatic renewal
- Include 7-day free trial period for new subscriptions
- Support multiple active subscriptions per user
- When cancelled after trial period, maintain access until subscription endDate (grace period)

**Payment Entity:**
- Records every transaction event from payment providers for auditing and support
- Should include fields like: ID, userID, subscriptionID, provider (hotmart/applepay), providerTransactionID, amount, currency, status, webhookData (JSON), processedAt, createdAt
- Log all webhook events from payment providers

**Implementation Requirements:**
- Follow the existing codebase patterns (separate files per layer: controller.go, service.go, repository.go, model.go)
- Implementation belongs to billing package
  -I.e, 
  -internal/model/billing/plan.go
  -internal/model/billing/subscription.go
  -internal/service/billing/service.go
  -internal/service/billing/plan.go
  -internal/service/billing/subscription.go
...
- Use MongoDB as the database following existing collection patterns
- Implement webhook endpoints to handle Hotmart and Apple Pay payment notifications
- Create middleware for subscription-based access control that checks active subscriptions to determine feature access
- Use the existing RBAC patterns and JWT authentication system
- Follow the v2 API naming convention for endpoints
- Use monetary.Amount type for all monetary values (not float64)
- Include comprehensive error handling using the existing errors package
- Implement proper validation for all entities
- Consider integration with existing user management system

**Access Control:**
- Middleware should check user's active subscriptions to determine feature permissions
- Grace period handling: cancelled subscriptions remain active until endDate
- Trial period logic: full access during 7-day trial, then require payment
- Support for multiple concurrent subscriptions (user gets union of all active subscription benefits)