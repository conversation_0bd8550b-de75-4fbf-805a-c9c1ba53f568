package financialdna

import (
	"fmt"
	"log"
	"time"

	"github.com/dsoplabs/dinbora-backend/internal/errors"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// FinancialDNATree represents the entire family tree for a user.
type FinancialDNATree struct {
	ObjectID              primitive.ObjectID    `json:"-" bson:"_id,omitempty"`
	ID                    string                `json:"id" bson:"-"`
	UserID                string                `json:"userID" bson:"userID"` // Reference to ID
	Members               []*FamilyMember       `json:"members" bson:"members"`
	TreeProgress          TreeProgress          `json:"treeProgress" bson:"treeProgress"`
	BreakCycles           BreakCycles           `json:"breakCycles" bson:"breakCycles"`
	FinancialDistribution FinancialDistribution `json:"financialDistribution" bson:"financialDistribution"`
	CreatedAt             time.Time             `json:"createdAt" bson:"createdAt"`
	UpdatedAt             time.Time             `json:"updatedAt" bson:"updatedAt"`
}

// New creates a new FinancialDNATree instance with initialized fields.
func New(userID string) *FinancialDNATree {
	return &FinancialDNATree{
		UserID:  userID,
		Members: []*FamilyMember{},
	}
}

// AddMember adds a new family member to the tree without handling relationships.
func (tree *FinancialDNATree) AddMember(name string, icon FamilyMemberIcon, financialStatus FinancialStatus) (*FamilyMember, error) {
	// Generate a unique ID for the family member
	ObjID := primitive.NewObjectID()

	// Create the new family member
	newMember := &FamilyMember{
		ObjectID:        ObjID,
		ID:              ObjID.Hex(),
		Name:            name,
		Icon:            icon,
		ParentIDs:       make([]string, 0), // Initialize as empty
		ChildrenIDs:     make([]string, 0), // Initialize as empty
		FinancialStatus: financialStatus,
		CreatedAt:       time.Now(),
		UpdatedAt:       time.Now(),
	}

	// Add the new member to the tree's Members slice
	tree.Members = append(tree.Members, newMember)

	return newMember, nil
}

func (tree *FinancialDNATree) AddParents(child *FamilyMember, fatherName, motherName string, fatherIcon, motherIcon FamilyMemberIcon, defaultFinancialStatus FinancialStatus) (*FamilyMember, *FamilyMember, error) {
	// Add the father
	father, err := tree.AddMember(fatherName, fatherIcon, defaultFinancialStatus)
	if err != nil {
		return nil, nil, errors.New(errors.Model, "error adding father", errors.Internal, err)
	}

	// Add the mother
	mother, err := tree.AddMember(motherName, motherIcon, defaultFinancialStatus)
	if err != nil {
		return nil, nil, errors.New(errors.Model, "error adding mother", errors.Internal, err)
	}
	mother.Icon = motherIcon

	// Update the child's ParentIDs
	for i, member := range tree.Members {
		if member.ID == child.ID {
			tree.Members[i].ParentIDs = []string{father.ID, mother.ID}
			tree.Members[i].UpdatedAt = time.Now()
			break
		}
	}

	// Update the father's ChildrenIDs
	for i, member := range tree.Members {
		if member.ID == father.ID {
			tree.Members[i].ChildrenIDs = append(tree.Members[i].ChildrenIDs, child.ID)
			tree.Members[i].UpdatedAt = time.Now()
			break
		}
	}

	// Update the mother's ChildrenIDs
	for i, member := range tree.Members {
		if member.ID == mother.ID {
			tree.Members[i].ChildrenIDs = append(tree.Members[i].ChildrenIDs, child.ID)
			tree.Members[i].UpdatedAt = time.Now()
			break
		}
	}

	return father, mother, nil
}

// AddChild adds a child to a parent and updates the relationships.
func (tree *FinancialDNATree) AddChild(parent *FamilyMember, childName string, childIcon FamilyMemberIcon, financialStatus FinancialStatus) (*FamilyMember, error) {
	// Add the child
	child, err := tree.AddMember(childName, childIcon, financialStatus)
	if err != nil {
		return nil, errors.New(errors.Model, "error adding child", errors.Internal, err)
	}

	// Update the parent's ChildrenIDs
	for i, member := range tree.Members {
		if member.ID == parent.ID {
			tree.Members[i].ChildrenIDs = append(tree.Members[i].ChildrenIDs, child.ID)
			tree.Members[i].UpdatedAt = time.Now()
			break
		}
	}

	// Update the child's ParentIDs
	for i, member := range tree.Members {
		if member.ID == child.ID {
			// Enforce binary tree constraint: at most 2 parents
			if len(member.ParentIDs) >= 2 {
				return nil, errors.New(errors.Model, "child already has 2 parents", errors.Validation, nil)
			}
			tree.Members[i].ParentIDs = append(tree.Members[i].ParentIDs, parent.ID)
			tree.Members[i].UpdatedAt = time.Now()
			break
		}
	}

	return child, nil
}

// AddChildren adds multiple children to a parent.
func (tree *FinancialDNATree) AddChildren(parent *FamilyMember, childNamePrefix string, childIcon FamilyMemberIcon, numChildren int, financialStatus FinancialStatus) ([]*FamilyMember, error) {
	children := make([]*FamilyMember, 0, numChildren)

	for i := 1; i <= numChildren; i++ {
		childName := fmt.Sprintf("%s %d", childNamePrefix, i)
		child, err := tree.AddChild(parent, childName, childIcon, financialStatus)
		if err != nil {
			return nil, errors.New(errors.Model, "error adding child", errors.Internal, err)
		}
		children = append(children, child)
	}

	return children, nil
}

// RemoveChild removes a child from a parent and from the tree.
func (tree *FinancialDNATree) RemoveChild(parent *FamilyMember, childID string) error {
	// If the child has children return an error
	child := tree.FindMember(childID)
	if len(child.ChildrenIDs) > 0 {
		return errors.New(errors.Model, "child has children and cannot be removed", errors.Validation, nil)
	}

	// Remove the child from the parent's ChildrenIDs
	for i, member := range tree.Members {
		if member.ID == parent.ID {
			for j, child := range member.ChildrenIDs {
				if child == childID {
					tree.Members[i].ChildrenIDs = append(tree.Members[i].ChildrenIDs[:j], tree.Members[i].ChildrenIDs[j+1:]...)
					tree.Members[i].UpdatedAt = time.Now()
					break
				}
			}
			break
		}
	}

	// Remove the parent from the child's ParentIDs
	for i, member := range tree.Members {
		if member.ID == childID {
			for j, parentID := range member.ParentIDs {
				if parentID == parent.ID {
					tree.Members[i].ParentIDs = append(tree.Members[i].ParentIDs[:j], tree.Members[i].ParentIDs[j+1:]...)
					tree.Members[i].UpdatedAt = time.Now()
					break
				}
			}
			break
		}
	}

	// Remove the child from the tree's Members slice
	for i, member := range tree.Members {
		if member.ID == childID {
			tree.Members = append(tree.Members[:i], tree.Members[i+1:]...)
			break
		}
	}

	return nil
}

// RemoveMember completely removes a member from the tree and updates all relationships.
// This removes the member from the Members slice and also removes any references to this member
// from other members' ParentIDs and ChildrenIDs.
func (tree *FinancialDNATree) RemoveMember(memberID string) error {
	// Find the member to be removed
	memberToRemove := tree.FindMember(memberID)
	if memberToRemove == nil {
		return errors.New(errors.Model, "member not found", errors.NotFound, nil)
	}

	// Remove this member from all parents' ChildrenIDs
	for _, parentID := range memberToRemove.ParentIDs {
		parent := tree.FindMember(parentID)
		if parent != nil {
			for i, member := range tree.Members {
				if member.ID == parentID {
					// Find and remove the member's ID from parent's ChildrenIDs
					for j, childID := range member.ChildrenIDs {
						if childID == memberID {
							tree.Members[i].ChildrenIDs = append(tree.Members[i].ChildrenIDs[:j], tree.Members[i].ChildrenIDs[j+1:]...)
							tree.Members[i].UpdatedAt = time.Now()
							break
						}
					}
					break
				}
			}
		}
	}

	// Remove this member from all children's ParentIDs
	for _, childID := range memberToRemove.ChildrenIDs {
		child := tree.FindMember(childID)
		if child != nil {
			for i, member := range tree.Members {
				if member.ID == childID {
					// Find and remove the member's ID from child's ParentIDs
					for j, parentID := range member.ParentIDs {
						if parentID == memberID {
							tree.Members[i].ParentIDs = append(tree.Members[i].ParentIDs[:j], tree.Members[i].ParentIDs[j+1:]...)
							tree.Members[i].UpdatedAt = time.Now()
							break
						}
					}
					break
				}
			}
		}
	}

	// Finally, remove the member from the tree's Members slice
	for i, member := range tree.Members {
		if member.ID == memberID {
			tree.Members = append(tree.Members[:i], tree.Members[i+1:]...)
			break
		}
	}

	return nil
}

// FindMember finds a family member by their ID.
func (tree *FinancialDNATree) FindMember(id string) *FamilyMember {
	for _, member := range tree.Members {
		if member.ID == id {
			return member
		}
	}
	return nil
}

func (tree *FinancialDNATree) FindAllParents(memberID string) ([]map[uint8]*FamilyMember, error) {
	var allParents []map[uint8]*FamilyMember
	member := tree.FindMember(memberID)
	if member == nil {
		return nil, errors.New(errors.Model, "member not found", errors.NotFound, nil)
	}

	// Helper function to recursively find parents with generation tracking
	var findParents func(parentID string, generation uint8) error
	findParents = func(parentID string, generation uint8) error {
		parent := tree.FindMember(parentID)
		if parent == nil {
			return nil // Skip if parent not found, don't treat as error
		}

		// Create a new map for this parent
		parentMap := make(map[uint8]*FamilyMember)
		parentMap[generation] = parent
		allParents = append(allParents, parentMap)

		// Recursively find parents of this parent
		for _, grandParentID := range parent.ParentIDs {
			if err := findParents(grandParentID, generation+1); err != nil {
				return err
			}
		}
		return nil
	}

	// Start with immediate parents (generation 1)
	for _, parentID := range member.ParentIDs {
		if err := findParents(parentID, 1); err != nil {
			return nil, errors.New(errors.Model, "error finding parents", errors.Internal, err)
		}
	}

	return allParents, nil
}

// --- New Formatted Response Structs ---

// FinancialDNATreeFormatted represents the tree with one member potentially formatted differently.
type FinancialDNATreeFormatted struct {
	ID                    string                `json:"id"`
	UserID                string                `json:"userId"`
	Members               []interface{}         `json:"members"` // Can hold *FamilyMember or *FinancialDNAFormatted
	TreeProgress          TreeProgress          `json:"treeProgress"`
	BreakCycles           BreakCycles           `json:"breakCycles"`
	FinancialDistribution FinancialDistribution `json:"financialDistribution"`
	CreatedAt             time.Time             `json:"createdAt"`
	UpdatedAt             time.Time             `json:"updatedAt"`
}

// FamilyMemberFormatted represents the top-level formatted response.
type FamilyMemberFormatted struct {
	ID              string                        `json:"id"`
	Name            string                        `json:"name"`
	Icon            FamilyMemberIcon              `json:"icon"`
	ParentIDs       []string                      `json:"parentIds"`
	Children        []*FinancialDNAChildFormatted `json:"children"`
	FinancialStatus FinancialStatus               `json:"financialStatus"`
	CreatedAt       time.Time                     `json:"createdAt"`
	UpdatedAt       time.Time                     `json:"updatedAt"`
}

// FinancialDNAChildFormatted represents the nested children level.
type FinancialDNAChildFormatted struct {
	ID              string                             `json:"id"`
	Name            string                             `json:"name"`
	Icon            FamilyMemberIcon                   `json:"icon"`
	GrandChildren   []*FinancialDNAGrandChildFormatted `json:"grandChildren"`
	FinancialStatus FinancialStatus                    `json:"financialStatus"`
	// Note: ParentIDs, CreatedAt, UpdatedAt are omitted as per the example for children
}

// FinancialDNAGrandChildFormatted represents the nested grandchildren level.
type FinancialDNAGrandChildFormatted struct {
	ID              string           `json:"id"`
	Name            string           `json:"name"`
	Icon            FamilyMemberIcon `json:"icon"`
	FinancialStatus FinancialStatus  `json:"financialStatus"`
	// Note: ParentIDs, ChildrenIDs, CreatedAt, UpdatedAt are omitted as per the example for grandchildren
}

// --- End New Formatted Response Structs ---

// FormatResponse formats the tree, replacing the member with targetMemberID
// with its nested format, leaving others as standard FamilyMember.
func (tree *FinancialDNATree) FormatResponse(targetMemberID string) (*FinancialDNATreeFormatted, error) {
	mixedResponse := &FinancialDNATreeFormatted{
		ID:                    tree.ID, // Assuming tree.ID holds the BSON ObjectID hex string
		UserID:                tree.UserID,
		Members:               make([]interface{}, 0, len(tree.Members)),
		TreeProgress:          tree.TreeProgress,
		BreakCycles:           tree.BreakCycles,
		FinancialDistribution: tree.FinancialDistribution,
		CreatedAt:             tree.CreatedAt,
		UpdatedAt:             tree.UpdatedAt,
	}

	foundTarget := false
	descendantIDsToExclude := make(map[string]bool)
	var tempFormattedTargetMember *FamilyMemberFormatted // Store the formatted target member temporarily

	// First pass: Find and format the target member, collect descendant IDs
	for _, member := range tree.Members {
		if member.ID == targetMemberID {
			foundTarget = true
			formattedMember, descendantIDs, err := tree.formatMemberNested(member.ID)
			if err != nil {
				// Return error immediately if formatting the target fails
				return nil, errors.New(errors.Model, "error formatting target member", errors.Internal, err)
			}
			tempFormattedTargetMember = formattedMember
			descendantIDsToExclude = descendantIDs
			break // Found and processed the target, no need to continue this loop
		}
	}

	if !foundTarget {
		// Handle case where the target member itself wasn't found
		return nil, errors.New(errors.Model, "target member for mixed formatting not found", errors.NotFound, nil)
	}

	// Second pass: Build the final members list, excluding descendants
	for _, member := range tree.Members {
		if member.ID == targetMemberID {
			// Add the pre-formatted target member
			mixedResponse.Members = append(mixedResponse.Members, tempFormattedTargetMember)
		} else {
			// Add other members only if they are NOT descendants of the target
			if _, exclude := descendantIDsToExclude[member.ID]; !exclude {
				mixedResponse.Members = append(mixedResponse.Members, member)
			}
		}
	}

	// Note: formatErr is not used here as we return immediately on target formatting error.
	// If other errors were possible, error handling might need adjustment.
	return mixedResponse, nil
}

// formatMemberNested formats a single member with nested children and grandchildren,
// and returns a map of descendant IDs included in the formatting.
func (tree *FinancialDNATree) formatMemberNested(memberID string) (*FamilyMemberFormatted, map[string]bool, error) {
	member := tree.FindMember(memberID)
	if member == nil {
		return nil, nil, errors.New(errors.Model, "member not found for nested formatting", errors.NotFound, nil)
	}

	formattedMember := &FamilyMemberFormatted{
		ID:              member.ID,
		Name:            member.Name,
		Icon:            member.Icon,
		ParentIDs:       member.ParentIDs,
		Children:        make([]*FinancialDNAChildFormatted, 0, len(member.ChildrenIDs)),
		FinancialStatus: member.FinancialStatus,
		CreatedAt:       member.CreatedAt,
		UpdatedAt:       member.UpdatedAt,
	}

	descendantIDs := make(map[string]bool)

	for _, childID := range member.ChildrenIDs {
		childMember := tree.FindMember(childID)
		if childMember == nil {
			log.Printf("Warning: Child member with ID %s not found for parent %s", childID, memberID)
			continue // Skip this child if not found, or handle error differently
		}
		descendantIDs[childID] = true // Mark child ID as included

		formattedChild := &FinancialDNAChildFormatted{
			ID:              childMember.ID,
			Name:            childMember.Name,
			Icon:            childMember.Icon,
			GrandChildren:   make([]*FinancialDNAGrandChildFormatted, 0, len(childMember.ChildrenIDs)),
			FinancialStatus: childMember.FinancialStatus,
		}

		for _, grandChildID := range childMember.ChildrenIDs {
			grandChildMember := tree.FindMember(grandChildID)
			if grandChildMember == nil {
				log.Printf("Warning: Grandchild member with ID %s not found for child %s", grandChildID, childID)
				continue // Skip this grandchild if not found
			}
			descendantIDs[grandChildID] = true // Mark grandchild ID as included

			formattedGrandChild := &FinancialDNAGrandChildFormatted{
				ID:              grandChildMember.ID,
				Name:            grandChildMember.Name,
				Icon:            grandChildMember.Icon,
				FinancialStatus: grandChildMember.FinancialStatus,
			}
			formattedChild.GrandChildren = append(formattedChild.GrandChildren, formattedGrandChild)
		}
		formattedMember.Children = append(formattedMember.Children, formattedChild)
	}

	return formattedMember, descendantIDs, nil
}
