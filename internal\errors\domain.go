package errors

import "fmt"

type DomainError struct {
	layer    Layer
	message  Message
	kind     Kind
	original error
}

func OldError(message Message, kind Kind, original error) *DomainError {
	return &DomainError{
		message:  message,
		kind:     kind,
		original: original,
	}
}

func New(layer Layer, message Message, kind Kind, original error) *DomainError {
	return &DomainError{
		layer:    layer,
		message:  message,
		kind:     kind,
		original: original,
	}
}

func (e *DomainError) Error() string {
	if e.original != nil {
		return fmt.Sprintf("%s: %s (kind:%s) (cause: %v)", e.layer, e.message, e.kind, e.original)
	}
	return fmt.Sprintf("%s: %s (kind:%s)", e.layer, e.message, e.kind)
}

func (d *DomainError) Kind() Kind {
	return d.kind
}

func (d *DomainError) Unwrap() error {
	return d.original
}

func (d *Message) GetMessage() string {
	return string(*d)
}
