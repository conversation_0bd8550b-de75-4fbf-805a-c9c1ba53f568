package facebook

import (
	"context"
	"io"
	"net/http"
	"net/url"

	"github.com/dsoplabs/dinbora-backend/internal/errors"
	"github.com/dsoplabs/dinbora-backend/internal/model"

	"github.com/dsoplabs/dinbora-backend/internal/service/facebook"
	"github.com/dsoplabs/dinbora-backend/internal/service/user"
	"github.com/labstack/echo/v4"
)

type Controller interface {
	// Routes
	RegisterRoutes(ctx context.Context, legacyGroup *echo.Group, currentGroup *echo.Group)

	// OAuth
	InitLogin() echo.HandlerFunc
	CallBackFromFacebook() echo.HandlerFunc

	// Register/Login
	CallBackFromFacebookRegister() echo.HandlerFunc
	CallBackFromFacebookLogin() echo.HandlerFunc

	// Utility
	UserInfoUtils() echo.HandlerFunc
}

type controller struct {
	Service facebook.Service
}

func New(userService user.Service) Controller {
	return &controller{
		Service: facebook.New(userService),
	}
}

// Routes
func (fc *controller) RegisterRoutes(ctx context.Context, legacyGroup *echo.Group, currentGroup *echo.Group) {
	authGroup := legacyGroup.Group("auth/")

	authGroup.GET("oauth2/facebook/", fc.InitLogin())
	authGroup.GET("oauth2/callback-facebook/", fc.CallBackFromFacebook())
	authGroup.POST("oauth2/callback/facebook/register/", fc.CallBackFromFacebookRegister())
	authGroup.POST("oauth2/callback/facebook/login/", fc.CallBackFromFacebookLogin())
}

// OAuth
func (fc *controller) InitLogin() echo.HandlerFunc {
	return func(c echo.Context) error {

		url, err := fc.Service.SetupOAuth2()
		if err != nil {
			return errors.HTTPStatus(err)
		}

		http.Redirect(c.Response().Writer, c.Request(), url, http.StatusTemporaryRedirect)

		return c.JSON(http.StatusTemporaryRedirect, "")
	}
}

func (fc *controller) CallBackFromFacebook() echo.HandlerFunc {
	return func(c echo.Context) error {
		state := c.Request().FormValue("state")
		code := c.Request().FormValue("code")

		if state != fc.Service.OauthState() || state == "" {
			// Check a redirect page to send the user if login fail.
			//http.Redirect(c.Response().Writer, c.Request(), "/?invalidlogin=true", http.StatusTemporaryRedirect)
			http.Redirect(c.Response().Writer, c.Request(), "/", http.StatusTemporaryRedirect)
			return errors.New(errors.Controller, "invalid OAuth state", errors.Unauthorized, nil)
		}

		if code == "" {
			c.Response().Writer.Write([]byte("Code Not Found to provide AccessToken..\n"))
			reason := c.Request().FormValue("error_reason")
			if reason == "user_denied" {
				c.Response().Writer.Write([]byte("User has denied Permission.."))
			}

			http.Redirect(c.Response().Writer, c.Request(), "/", http.StatusTemporaryRedirect)
			return errors.New(errors.Controller, "code not found", errors.Validation, nil)
		}

		token, err := fc.Service.CallBackFromFacebook(code)
		if err != nil || token == nil {
			http.Redirect(c.Response().Writer, c.Request(), "/", http.StatusTemporaryRedirect)
			return err
		}

		facebookUserdetails, err := fc.Service.GetUserInfoFromFacebook(token.AccessToken)
		if err != nil {
			http.Redirect(c.Response().Writer, c.Request(), "/", http.StatusTemporaryRedirect)
			return err
		}
		referralCode := c.QueryParam("referral")

		accessToken, err := fc.Service.Register(facebookUserdetails, &model.Onboarding{}, referralCode)
		if err != nil {
			accessToken, err = fc.Service.Login(facebookUserdetails)
			if err != nil {
				http.Redirect(c.Response().Writer, c.Request(), "/", http.StatusTemporaryRedirect)
				return err
			}
		}

		return c.JSON(http.StatusOK, accessToken)
	}
}

// Register/Login
func (gc *controller) CallBackFromFacebookRegister() echo.HandlerFunc {
	return func(c echo.Context) error {
		facebookRegisterInformation := gc.Service.RegisterInformation()

		if err := c.Bind(&facebookRegisterInformation); err != nil {
			http.Redirect(c.Response().Writer, c.Request(), "/", http.StatusTemporaryRedirect)
			return errors.New(errors.Controller, "invalid facebook token", errors.Validation, nil)
		}

		referralCode := c.QueryParam("referral")

		facebookUserDetails, err := gc.Service.GetUserInfoFromFacebook(facebookRegisterInformation.Access)
		if err != nil {
			http.Redirect(c.Response().Writer, c.Request(), "/", http.StatusTemporaryRedirect)
			return err
		}

		accessData, err := gc.Service.Register(facebookUserDetails, facebookRegisterInformation.Onboarding, referralCode)
		if err != nil {
			http.Redirect(c.Response().Writer, c.Request(), "/", http.StatusTemporaryRedirect)
			return err
		}

		return c.JSON(http.StatusOK, accessData)
	}
}

func (gc *controller) CallBackFromFacebookLogin() echo.HandlerFunc {
	return func(c echo.Context) error {
		facebookRegisterInformation := gc.Service.RegisterInformation()

		if err := c.Bind(&facebookRegisterInformation); err != nil {
			http.Redirect(c.Response().Writer, c.Request(), "/", http.StatusTemporaryRedirect)
			return errors.New(errors.Controller, "invalid facebook token", errors.Validation, nil)
		}

		facebookUserDetails, err := gc.Service.GetUserInfoFromFacebook(facebookRegisterInformation.Access)
		if err != nil {
			http.Redirect(c.Response().Writer, c.Request(), "/", http.StatusTemporaryRedirect)
			return err
		}

		accessData, err := gc.Service.Login(facebookUserDetails)
		if err != nil {
			http.Redirect(c.Response().Writer, c.Request(), "/", http.StatusTemporaryRedirect)
			return err
		}

		return c.JSON(http.StatusOK, accessData)
	}
}

// Utility
func (fc *controller) UserInfoUtils() echo.HandlerFunc {
	return func(c echo.Context) error {

		resp, err := http.Get("https://graph.facebook.com/me?fields=id,name,email&access_token=" + url.QueryEscape("token.AccessToken"))
		if err != nil {
			http.Redirect(c.Response().Writer, c.Request(), "/", http.StatusTemporaryRedirect)
			return err
		}
		defer resp.Body.Close()

		response, err := io.ReadAll(resp.Body)
		if err != nil {
			http.Redirect(c.Response().Writer, c.Request(), "/", http.StatusTemporaryRedirect)
			return err
		}

		c.Response().Writer.Write([]byte("Hello, I'm protected\n"))
		c.Response().Writer.Write([]byte(string(response)))

		return c.JSON(http.StatusOK, "")
	}
}
