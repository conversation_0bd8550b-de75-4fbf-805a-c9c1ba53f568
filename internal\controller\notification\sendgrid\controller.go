package sendgrid

import (
	"context"

	"github.com/dsoplabs/dinbora-backend/internal/api/middlewares"
	"github.com/labstack/echo/v4"
)

type Controller interface {
	// Routes
	RegisterRoutes(ctx context.Context, legacyGroup *echo.Group, currentGroup *echo.Group)
}

type controller struct {
	//Service userService.Service
}

func New() Controller {
	return &controller{
		//Service: service,
	}
}

func (nc *controller) RegisterRoutes(ctx context.Context, legacyGroup *echo.Group, currentGroup *echo.Group) {
	notificationGroup := legacyGroup.Group("notification/", middlewares.AuthGuard())
	notificationGroup.GET("", nil)
}
