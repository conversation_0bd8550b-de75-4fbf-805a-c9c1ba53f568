package league

import (
	"time"

	"github.com/dsoplabs/dinbora-backend/internal/model/league"
)

// DTOs for request/response bodies

type CreateLeagueRequest struct {
	Name      string    `json:"name" validate:"required,min=3,max=50"`
	StartDate time.Time `json:"startDate" validate:"required"`
	EndDate   time.Time `json:"endDate" validate:"required"` // Add validation: EndDate after StartDate
}

type JoinLeagueRequest struct {
	Code string `json:"code" validate:"required"`
	// UserName   string `json:"userName" validate:"required,min=1"`           // Removed: Will be fetched from UserService
	// AvatarURL  string `json:"avatarUrl,omitempty" validate:"omitempty,url"` // Removed: Will be fetched from UserService, and field is PhotoURL
}

type PatchRequest struct {
	Name *string `json:"name,omitempty" validate:"omitempty,min=3,max=50"`
}

type FindResponse struct {
	ID              string                `json:"id,omitempty" bson:"-"`
	Code            string                `json:"code,omitempty" bson:"code,omitempty"` // Can be generated on demand
	Name            string                `json:"name" bson:"name"`
	BackgroundColor string                `json:"backgroundColor" bson:"backgroundColor"`
	OwnerUserID     string                `json:"ownerUserID" bson:"ownerUserID"`
	GeneralStatus   GeneralStatus         `json:"generalStatus" bson:"generalStatus"`
	Podium          []*league.UserRanking `json:"podium" bson:"podium"`
	CurrentSeason   league.Season         `json:"currentSeason" bson:"currentSeason"`
}

type FindAllLeagueLearningResponse struct {
	LeaguesLearning []*LeagueLearning     `json:"leagues"`
	Podium          []*league.UserRanking `json:"podium"`
}

type LeagueLearning struct {
	ID   string `json:"id,omitempty"`
	Name string `json:"name"`
}

type GeneralStatus struct {
	LeagueDays        int `json:"leagueDays"`
	TransactionStreak int `json:"transactionStreak"`
}

type InviteDetailsResponse struct {
	Name           string `json:"name"`
	OwnerUserName  string `json:"ownerUserName"`
	SeasonDuration int    `json:"seasonDuration"`
}
