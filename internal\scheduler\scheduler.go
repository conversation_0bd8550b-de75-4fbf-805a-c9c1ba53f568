package scheduler

import (
	"context"
	"errors"
	"log"
	"time"

	"github.com/dsoplabs/dinbora-backend/internal/service/dashboard"
	"github.com/dsoplabs/dinbora-backend/internal/service/user"
	"go.mongodb.org/mongo-driver/mongo"
)

// Scheduler interface defines the scheduler operations
type Scheduler interface {
	Start() error
	Stop() error
}

type scheduler struct {
	DashboardService dashboard.Service
	UserService      user.Service
	timer            *time.Timer
	stopChan         chan bool
	running          bool
}

// New creates a new scheduler instance
func New(dashboardService dashboard.Service, userService user.Service) Scheduler {
	return &scheduler{
		DashboardService: dashboardService,
		UserService:      userService,
		stopChan:         make(chan bool),
		running:          false,
	}
}

// Start is now the master function. It first runs a synchronous catch-up,
// then launches the asynchronous scheduler for future jobs.
func (s *scheduler) Start() error {
	if s.running {
		return nil // Already running
	}
	s.running = true // Set state immediately

	log.Println("Scheduler starting...")

	// --- CATCH-UP LOGIC ---
	// Run the catch-up process synchronously before starting the main loop.
	// This ensures the system state is consistent before the app is fully "live".
	log.Println("Starting snapshot catch-up check...")
	if err := s.catchUpMissedSnapshots(context.Background()); err != nil {
		// We log the error but don't stop the scheduler from starting for future jobs.
		// You might want to handle this differently (e.g., a fatal error).
		log.Printf("Error during snapshot catch-up: %v", err)
	}
	log.Println("Snapshot catch-up check complete.")

	// --- FUTURE SCHEDULING ---
	// Launch the regular run loop in the background for future jobs.
	go s.run()

	return nil
}

// catchUpMissedSnapshots checks for and processes any months that were missed.
func (s *scheduler) catchUpMissedSnapshots(ctx context.Context) error {
	// 1. Get the date of the most recent snapshot from the database.
	lastSnapshotDate, err := s.DashboardService.FindLatestNetWorthSnapshot(ctx)

	// 2. Handle the case where no snapshots exist yet (e.g., fresh database).
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) { // Or your custom repository not-found error
			log.Println("No previous snapshots found. No catch-up required.")
			return nil // This is not an error, just a state.
		}
		return err // A real database error occurred.
	}

	// 3. Determine which months were missed.
	// We create a "cursor" starting from the month *after* the last snapshot.
	// Using UTC for the cursor is crucial for consistent, timezone-agnostic calculations.
	cursorMonth := time.Date(lastSnapshotDate.Year(), lastSnapshotDate.Month()+1, 1, 0, 0, 0, 0, time.UTC)
	now := time.Now().UTC()

	// Loop from the month after the last snapshot up to the beginning of the current month.
	for cursorMonth.Before(now) {
		// Check if we are trying to process the current month. We only want to process *past* full months.
		if cursorMonth.Year() == now.Year() && cursorMonth.Month() == now.Month() {
			break
		}

		log.Printf("-> Missed snapshot detected for month: %s. Processing now.", cursorMonth.Format("2006-01"))
		s.createMonthlySnapshots(ctx, cursorMonth)

		// Move to the next month to check.
		cursorMonth = cursorMonth.AddDate(0, 1, 0)
	}

	return nil
}

func (s *scheduler) run() {
	defer log.Println("Scheduler run loop terminated.")

	// --- CHANGED ---
	// Calculate the first run time to be the LAST second of the CURRENT month.
	now := time.Now()
	firstOfNextMonth := time.Date(now.Year(), now.Month()+1, 1, 0, 0, 0, 0, now.Location())
	nextRun := firstOfNextMonth.Add(-1 * time.Second)

	log.Printf("Scheduler starting. Next monthly snapshot will run at %v (in %v)", nextRun, nextRun.Sub(now))

	s.timer = time.NewTimer(nextRun.Sub(now))

	for {
		select {
		case <-s.timer.C:
			// --- CHANGED ---
			// 1. Determine which month to snapshot. Since the timer fired at the end of the
			//    month, the current time's month is the one we need to snapshot.
			monthToSnapshot := time.Now()
			// Create a clean identifier for the month (e.g., "2025-05-01 00:00:00 UTC").
			// Using UTC is best practice for identifiers.
			snapshotIdentifier := time.Date(monthToSnapshot.Year(), monthToSnapshot.Month(), 1, 0, 0, 0, 0, time.UTC)

			// 2. Call the worker, telling it exactly which month to process.
			s.createMonthlySnapshots(context.Background(), snapshotIdentifier)

			// --- CHANGED ---
			// 3. Work is done, calculate the end of the *next* month for the next run.
			//    We use a fresh time.Now() in case the job took a while to run.
			currentTime := time.Now()
			firstOfNextMonth := time.Date(currentTime.Year(), currentTime.Month()+1, 1, 0, 0, 0, 0, currentTime.Location())
			nextRun := firstOfNextMonth.Add(-1 * time.Second)
			log.Printf("Snapshot task complete. Next run scheduled for %v (in %v)", nextRun, nextRun.Sub(currentTime))

			// Reset the timer for the new duration.
			s.timer.Reset(nextRun.Sub(currentTime))

		case <-s.stopChan:
			if !s.timer.Stop() {
				select {
				case <-s.timer.C:
				default:
				}
			}
			s.running = false
			log.Println("Scheduler stopped")
			return
		}
	}
}

// Stop stops the scheduler
func (s *scheduler) Stop() error {
	if !s.running {
		return nil // Already stopped
	}
	select {
	case s.stopChan <- true:
	default:
		log.Println("Stop signal already sent or channel blocked.")
	}
	return nil
}

// --- CHANGED ---
// createMonthlySnapshots now accepts the specific month it needs to process.
func (s *scheduler) createMonthlySnapshots(ctx context.Context, snapshotForMonth time.Time) {
	log.Printf("Starting monthly snapshot creation for month: %s", snapshotForMonth.Format("2006-01"))

	users, err := s.UserService.FindAll(ctx)
	if err != nil {
		log.Printf("Error fetching users for monthly snapshots: %v", err)
		return
	}

	// Capture the creation timestamp ONCE at the start of the job.
	creationTime := time.Now()
	successCount := 0
	errorCount := 0

	for _, user := range users {
		err := s.DashboardService.CreateMonthlySnapshot(ctx, user.ID, snapshotForMonth, creationTime)
		if err != nil {
			log.Printf("Error creating monthly snapshot for user %s: %v", user.ID, err)
			errorCount++
		} else {
			successCount++
		}
	}

	log.Printf("Monthly snapshot creation completed. Success: %d, Errors: %d, Total: %d",
		successCount, errorCount, len(users))
}
