package content

import (
	"fmt"
	"log"
	"strings"
)

type Lesson struct {
	Name         string            `json:"name" bson:"name"`
	Identifier   string            `json:"identifier" bson:"identifier"`
	Logo         string            `json:"logo" bson:"logo"`
	Color        string            `json:"color" bson:"color"`
	Order        uint8             `json:"order" bson:"order"`
	Requirements []string          `json:"requirements" bson:"requirements"`
	Advertising  LessonAdvertising `json:"advertising" bson:"advertising"`
	Content      []*LessonContent  `json:"content" bson:"content"`
}

type LessonContent struct {
	Image       string          `json:"image" bson:"image"`
	Identifier  string          `json:"identifier" bson:"identifier"`
	Description string          `json:"description" bson:"description"`
	Next        string          `json:"next,omitempty" bson:"next,omitempty"`
	Choices     []*LessonChoice `json:"choices,omitempty" bson:"choices,omitempty"`
}

type LessonChoice struct {
	Name       string          `json:"name,omitempty" bson:"name"`
	Identifier string          `json:"identifier,omitempty" bson:"identifier"`
	Next       string          `json:"next" bson:"next"`
	Choices    []*LessonChoice `json:"choices,omitempty" bson:"choices,omitempty"`
}

type LessonAdvertising struct {
	Banner    string `json:"banner" bson:"banner"`
	BannerURL string `json:"bannerURL" bson:"bannerURL"`
}

func (l *Lesson) GetContent(identifier string) *LessonContent {
	for _, content := range l.Content {
		if content.Identifier == identifier {
			return content
		}
	}
	return nil
}

func (l *Lesson) GetNext(identifier string, choice string) string {

	for _, content := range l.Content {
		if content.Identifier == identifier {
			if content.Next != "" {
				return content.Next
			}
			for _, ch := range content.Choices {
				next := ch.GetNext(choice)
				if next != "" {
					return next
				}
			}
		}
	}

	return ""
}

func (lch *LessonChoice) GetNext(choice string) string {

	if lch.Identifier == choice {
		return lch.Next
	}

	for _, ch := range lch.Choices {
		next := ch.GetNext(choice)
		if next != "" {
			return next
		}
	}

	return ""
}

func (l *Lesson) ValidateCreate() error {
	if l.Name == "" {
		return ErrLessonRequiredName
	}

	if l.Logo == "" {
		return ErrLessonRequiredLogo
	}

	if l.Identifier == "" {
		return errLessonRequiredIdentifier
	}

	if l.Content == nil {
		return ErrLessonContentRequired
	}

	// Validates if there is two content with same identifier.
	uniqueKeys := map[string]bool{
		"coin":    true,
		"diamond": true,
	}

	for _, content := range l.Content {
		if content != nil && content.Identifier != "" {
			if uniqueKeys[content.Identifier] {
				return fmt.Errorf("%w: %s", ErrLessonContentIdentifierShouldBeUnique, content.Identifier)
			} else {
				uniqueKeys[content.Identifier] = true
			}
		}
	}

	for _, content := range l.Content {
		if content != nil && content.Identifier != "" {
			if content.Next != "" {
				if _, ok := uniqueKeys[content.Next]; !ok {
					log.Printf("Content pointing to invalid key %s", content.Identifier)
					return ErrLessonContentOrLessonChoiceNextInvalidValue
				} else if content.Next == content.Identifier {
					log.Printf("Content pointing to itself %s", content.Identifier)
					return ErrLessonContentOrLessonChoiceNextInvalidValue
				}
			} else {
				if content.Choices != nil && len(content.Choices) > 0 {
					for _, choice := range content.Choices {
						if choice.Next != "" {
							if _, ok := uniqueKeys[choice.Next]; !ok || choice.Next == content.Identifier {
								log.Printf("Content pointing to itself or invalid key %s", content.Identifier)
								return ErrLessonContentOrLessonChoiceNextInvalidValue
							}
						}
					}
				} else {
					return ErrLessonContentOrLessonChoiceShouldHaveNext
				}
			}
		}
	}

	return nil
}

func (l *Lesson) PrepareCreate() error {
	l.Name = strings.TrimSpace(l.Name)
	l.Logo = strings.TrimSpace(l.Logo)

	if err := l.ValidateCreate(); err != nil {
		return err
	}

	if l.Content != nil {
		for _, content := range l.Content {
			if err := content.PrepareCreate(); err != nil {
				return err
			}
		}
	}

	return nil
}

func (lc *LessonContent) ValidateCreate() error {

	if lc.Description == "" {
		return ErrLessonContentRequiredDescription
	}

	if lc.Identifier == "" {
		return ErrLessonContentRequiredIdentifier
	}

	if lc.Next == "" {
		isValid := true
		visitedChoices := map[string]bool{}
		for _, lch := range lc.Choices {
			isValid = isValid && isChoiceWithValidNext(lch, visitedChoices, 0)
			if !isValid {
				return ErrLessonContentOrLessonChoiceShouldHaveNext
			}
		}
	}

	return nil
}

func isChoiceWithValidNext(choice *LessonChoice, visitedChoices map[string]bool, depth uint8) bool {
	visitedChoices[fmt.Sprintf("%s-%d", choice.Identifier, depth)] = true
	if choice.Next == "" && choice.Choices != nil && len(choice.Choices) > 0 {
		isValid := true
		for _, choicesChoice := range choice.Choices {
			if _, ok := visitedChoices[choicesChoice.Identifier]; !ok {
				isValid = isValid && isChoiceWithValidNext(choicesChoice, visitedChoices, depth+1)
				if !isValid {
					return false
				}
			}
		}
	} else if choice.Next == "" {
		return false
	}

	return true
}

func (lc *LessonContent) PrepareCreate() error {
	lc.Image = strings.TrimSpace(lc.Image)
	lc.Description = strings.TrimSpace(lc.Description)
	lc.Identifier = strings.ToLower(strings.TrimSpace(lc.Identifier))

	if err := lc.ValidateCreate(); err != nil {
		return err
	}

	for _, choice := range lc.Choices {
		if err := choice.PrepareCreate(); err != nil {
			return err
		}
	}

	return nil
}

func (lch *LessonChoice) PrepareCreate() error {
	lch.Name = strings.TrimSpace(lch.Name)
	lch.Identifier = strings.TrimSpace(lch.Identifier)

	if err := lch.ValidateCreate(); err != nil {
		return err
	}

	return nil
}

func (lch *LessonChoice) ValidateCreate() error {
	if lch.Name == "" {
		return ErrLessonChoiceRequiredName
	}

	if lch.Identifier == "" {
		return ErrLessonChoiceRequiredIdentifier
	}

	return nil
}

func (l *Lesson) PrepareUpdate() error {
	if err := l.ValidateUpdate(); err != nil {
		return err
	}

	if l.Content != nil {
		for _, content := range l.Content {
			if err := content.PrepareUpdate(); err != nil {
				return err
			}
		}
	}
	return nil
}

func (l *Lesson) ValidateUpdate() error {
	return l.ValidateCreate()
}

func (lc *LessonContent) PrepareUpdate() error {
	if err := lc.ValidateUpdate(); err != nil {
		return err
	}

	for _, choice := range lc.Choices {
		if err := choice.PrepareUpdate(); err != nil {
			return err
		}
	}

	return nil
}

func (lc *LessonContent) ValidateUpdate() error {
	return lc.ValidateCreate()
}

func (lch *LessonChoice) PrepareUpdate() error {
	if err := lch.ValidateUpdate(); err != nil {
		return err
	}

	return nil
}

func (lch *LessonChoice) ValidateUpdate() error {
	return lch.ValidateCreate()
}
