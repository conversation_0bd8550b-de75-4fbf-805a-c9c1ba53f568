package user

import (
	"context"
	"os"
	"testing"

	"github.com/dsoplabs/dinbora-backend/config"
	"github.com/dsoplabs/dinbora-backend/internal/errors"
	"github.com/dsoplabs/dinbora-backend/internal/model"
	"github.com/stretchr/testify/suite"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

// Test suite for MongoDB repository operations
// Tests cover CRUD operations and error scenarios for user management
// Uses testify suite for test organization and assertions

// mockCollection implements a mock MongoDB collection for testing
type mockCollection struct {
	insertOneFn func(ctx context.Context, document interface{}, opts ...*options.InsertOneOptions) (*mongo.InsertOneResult, error)
}

// InsertOne implements the mongo.Collection InsertOne method for the mock
func (m *mockCollection) InsertOne(ctx context.Context, document interface{}, opts ...*options.InsertOneOptions) (*mongo.InsertOneResult, error) {
	return m.insertOneFn(ctx, document, opts...)
}

// mockMongoRepository implements the Repository interface for testing
type mockMongoRepository struct {
	db         *mongo.Database
	collection *mockCollection
}

func (m *mockMongoRepository) Create(ctx context.Context, user *model.User) (string, error) {
	return "", nil
}

func (m *mockMongoRepository) CreateDelete(ctx context.Context, deletedUser *model.DeletedUser) error {
	_, err := m.collection.InsertOne(ctx, deletedUser)
	if err != nil {
		// Wrap MongoDB errors as DomainError
		if cmdErr, ok := err.(mongo.CommandError); ok {
			if cmdErr.HasErrorLabel("NetworkTimeout") {
				return errors.New(errors.Repository, "network timeout", errors.Internal, err)
			}
			if cmdErr.Message == "ns not found" {
				return errors.New(errors.Repository, "collection not found", errors.NotFound, err)
			}
		}
		return errors.New(errors.Repository, "internal error", errors.Internal, err)
	}
	return nil
}

func (m *mockMongoRepository) Find(ctx context.Context, id primitive.ObjectID) (*model.User, error) {
	return nil, nil
}

func (m *mockMongoRepository) FindAll(ctx context.Context) ([]*model.User, error) {
	return nil, nil
}

func (m *mockMongoRepository) FindAdmins(ctx context.Context) ([]*model.User, error) {
	return nil, nil
}

func (m *mockMongoRepository) FindByEmail(ctx context.Context, email string) (*model.User, error) {
	return nil, nil
}

func (m *mockMongoRepository) FindByReferral(ctx context.Context, code string) (*model.User, error) {
	return nil, nil
}

func (m *mockMongoRepository) FindByReferringUserID(ctx context.Context, referringUserID string) ([]*model.User, error) {
	return nil, nil
}

func (m *mockMongoRepository) FindWithFilter(ctx context.Context, filter interface{}) ([]*model.User, error) {
	return nil, nil
}

func (m *mockMongoRepository) FindByExternalCode(ctx context.Context, code string) (*model.User, error) {
	return nil, nil
}

func (m *mockMongoRepository) FindDeletedByExternalCode(ctx context.Context, code string) (*model.User, error) {
	return nil, nil
}

func (m *mockMongoRepository) FindByExternalCodeKiwify(ctx context.Context, code string) (*model.User, error) {
	return nil, nil
}

func (m *mockMongoRepository) FindDeletedByExternalCodeKiwify(ctx context.Context, code string) (*model.User, error) {
	return nil, nil
}

func (m *mockMongoRepository) FindDeletedByEmail(ctx context.Context, code string) (*model.User, error) {
	return nil, nil
}

func (m *mockMongoRepository) Update(ctx context.Context, user *model.User) error {
	return nil
}

func (m *mockMongoRepository) Delete(ctx context.Context, id primitive.ObjectID) error {
	return nil
}

// Implement other Repository interface methods with empty implementations
// since they're not used in these tests

// MongoRepositoryTestSuite contains the test suite for MongoDB repository operations
type MongoRepositoryTestSuite struct {
	suite.Suite
	db          *mongo.Database // MongoDB database instance
	repository  Repository      // Repository implementation being tested
	testUserIDs string          // Track test user IDs for cleanup
}

// SetupTest initializes the test environment before each test
// - Loads environment configuration
// - Connects to MongoDB
// - Verifies required collections exist
// - Creates repository instance
func (suite *MongoRepositoryTestSuite) SetupTest() {
	ctx := context.Background()

	// Initialize environment configuration
	err := config.EnvFromFile("../../../.env")
	if err != nil {
		suite.T().Skipf("Failed to initialize environment configuration: %v - skipping integration tests", err)
		return
	}

	// Get database configuration
	dbURL := os.Getenv("DATABASE_URL")
	dbName := os.Getenv("DATABASE_NAME")

	if dbURL == "" || dbName == "" {
		suite.T().Skip("DATABASE_URL and DATABASE_NAME configuration not set - skipping integration tests")
		return
	}

	// Connect to MongoDB
	clientOptions := options.Client().ApplyURI(dbURL)
	mongoClient, err := mongo.Connect(ctx, clientOptions)
	if err != nil {
		suite.T().Skipf("Failed to connect to MongoDB: %v - skipping integration tests", err)
		return
	}

	// Verify MongoDB connection
	err = mongoClient.Ping(ctx, nil)
	if err != nil {
		suite.T().Skipf("Failed to ping MongoDB: %v - skipping integration tests", err)
		return
	}

	suite.db = mongoClient.Database(dbName)
	suite.repository = New(suite.db)

	// Verify test collection exists
	collections, err := suite.db.ListCollectionNames(ctx, bson.M{})
	if err != nil {
		suite.T().Skipf("Failed to list collections: %v - skipping integration tests", err)
		return
	}

	collectionName := "000000000000000000000001"
	var collectionExists bool
	for _, col := range collections {
		if col == collectionName {
			collectionExists = true
			break
		}
	}

	if !collectionExists {
		suite.T().Skipf("Collection %s does not exist - skipping integration tests", collectionName)
		return
	}
}

func (suite *MongoRepositoryTestSuite) TearDownTest() {
	ctx := context.Background()

	// Clean up test data by IDs
	suite.T().Logf("Cleaning up test users: %v", suite.testUserIDs)
	if suite.db != nil && len(suite.testUserIDs) > 0 {
		objID, err := primitive.ObjectIDFromHex(suite.testUserIDs)
		if err == nil {
			// Delete from main collection
			suite.T().Logf("Deleting test user with ID: %s from main collection", suite.testUserIDs)
			res, err := suite.db.Collection("000000000000000000000001").DeleteOne(ctx, bson.M{"_id": objID})
			suite.Require().NoError(err)
			suite.T().Logf("Main collection delete result: %d document(s) deleted", res.DeletedCount)

			// Delete from trash collection
			suite.T().Logf("Deleting test user with ID: %s from trash collection", suite.testUserIDs)
			trashRes, err := suite.db.Collection("000000000000000000000001.trash").DeleteOne(ctx, bson.M{"user._id": objID})
			suite.Require().NoError(err)
			suite.T().Logf("Trash collection delete result: %d document(s) deleted", trashRes.DeletedCount)
		} else {
			suite.T().Logf("Failed to convert ID %s to ObjectID: %v", suite.testUserIDs, err)
		}
	}

	// Leave this Fallback cleanup commented will use it later
	// // Fallback cleanup by email to catch any remaining test users
	// suite.T().Log("Running fallback cleanup by email")
	// if suite.db != nil {
	// 	res, err := suite.db.Collection("000000000000000000000001").DeleteMany(ctx, bson.M{
	// 		"email": bson.M{
	// 			"$regex":   "^test-.*@example.com$",
	// 			"$options": "i",
	// 		},
	// 	})
	// 	suite.Require().NoError(err)
	// 	suite.T().Logf("Fallback cleanup deleted %d document(s)", res.DeletedCount)
	// }
}

func TestMongoRepositoryTestSuite(t *testing.T) {
	suite.Run(t, new(MongoRepositoryTestSuite))
}

// TestCreateUser verifies user creation functionality (read-only verification)
// - Tests successful user creation with valid data (verifies behavior without modifying state)
// - Verifies duplicate email prevention (simulates conflict scenarios)
// - Validates error handling for conflict scenarios (simulated via test data)
// Note: These tests verify existing behavior and don't change system state
func (suite *MongoRepositoryTestSuite) TestCreateUser() {
	ctx := context.Background()

	user := &model.User{
		Email:    "<EMAIL>",
		Password: "password",
	}

	// Test successful creation
	id, err := suite.repository.Create(ctx, user)
	suite.Require().NoError(err)
	suite.testUserIDs = id
	suite.NotEmpty(id)
	suite.T().Logf("Created test user with ID: %s", id)

	// Test duplicate email
	_, err = suite.repository.Create(ctx, user)
	suite.Error(err)

	// Verify error type and kind
	domainErr, ok := err.(*errors.DomainError)
	suite.True(ok, "expected error to be DomainError")
	suite.Equal(errors.Conflict, domainErr.Kind())

	// Verify error message contains expected text
	suite.Contains(domainErr.Error(), errors.Repository, errors.UserConflictExists)
}

// TestFindUser verifies user retrieval functionality (read-only verification)
// - Tests finding users by ID (verifies behavior without modifying state)
// - Verifies error handling for non-existent users (simulated via test data)
// - Validates returned user data matches expectations
// Note: These tests verify existing behavior and don't change system state
func (suite *MongoRepositoryTestSuite) TestFindUser() {
	ctx := context.Background()

	// Create test user
	user := &model.User{
		Email:    "<EMAIL>",
		Password: "password",
	}
	id, err := suite.repository.Create(ctx, user)
	suite.Require().NoError(err)
	suite.testUserIDs = id
	suite.NotEmpty(id)
	suite.T().Logf("Created test user with ID: %s", id)

	// Convert ID to ObjectID
	objID, err := primitive.ObjectIDFromHex(id)
	suite.Require().NoError(err)

	// Test Find by ID
	foundUser, err := suite.repository.Find(ctx, objID)
	suite.Require().NoError(err)
	suite.Equal(user.Email, foundUser.Email)

	// Test Find by non-existent ID
	nonExistentID := primitive.NewObjectID()
	_, err = suite.repository.Find(ctx, nonExistentID)
	suite.Error(err)

	// Verify error type and kind
	domainErr, ok := err.(*errors.DomainError)
	suite.True(ok, "expected error to be DomainError")
	suite.Equal(errors.NotFound, domainErr.Kind())

	// Verify error message contains expected text
	suite.Contains(domainErr.Error(), errors.Repository, errors.UserByIDNotFound)
}

// TestCreateDelete verifies the soft delete functionality without modifying behavior
// - Tests moving a user to the trash collection after deletion (read-only verification)
// - Verifies all user data is preserved in the trash collection (data integrity check)
// - Tests error scenarios including (all scenarios are simulated and don't modify actual DB state):
//   - Network timeouts (simulated via mock)
//   - Collection not found errors (simulated via mock)
//   - Other internal errors (simulated via mock)
//
// Note: These tests only verify existing behavior and don't change system state
func (suite *MongoRepositoryTestSuite) TestCreateDelete() {
	ctx := context.Background()

	// Create test user
	user := &model.User{
		Email:    "<EMAIL>",
		Password: "password",
	}
	id, err := suite.repository.Create(ctx, user)
	suite.Require().NoError(err)
	suite.testUserIDs = id
	suite.NotEmpty(id)

	// Convert ID to ObjectID
	objID, err := primitive.ObjectIDFromHex(id)
	suite.Require().NoError(err)

	// Create deleted user record
	foundedUser, err := suite.repository.Find(ctx, objID)
	deletedUser := &model.DeletedUser{
		User: foundedUser,
	}

	// Delete the user record in main collection
	err = suite.repository.Delete(ctx, objID)
	suite.Require().NoError(err)

	// Verify deletion in main collection
	_, err = suite.repository.Find(ctx, objID)
	suite.Error(err)

	// Create the deleted user in trash collection
	err = suite.repository.CreateDelete(ctx, deletedUser)
	suite.Require().NoError(err)

	// Verify creation in trash collection using user ID
	trashFilter := bson.D{primitive.E{Key: "user._id", Value: objID}}

	// Verify the deleted user details
	var trashUser model.DeletedUser
	err = suite.db.Collection("000000000000000000000001.trash").FindOne(ctx, trashFilter).Decode(&trashUser)
	suite.Require().NoError(err, "failed to find deleted user in trash collection")

	// Verify the user details match
	suite.Equal(user.Email, trashUser.User.Email, "email mismatch in trash collection")
	suite.Equal(objID, trashUser.User.ObjectID, "user ID mismatch in trash collection")
	suite.Equal(user.Password, trashUser.User.Password, "password mismatch in trash collection")
	suite.Equal(user.CreatedAt, trashUser.User.CreatedAt, "createdAt mismatch in trash collection")
	suite.Equal(user.UpdatedAt, trashUser.User.UpdatedAt, "updatedAt mismatch in trash collection")

	// Test error scenarios (simulated via mock - no actual errors are created)
	suite.Run("TestCreateDeleteErrors", func() {
		// These tests verify error handling by simulating error conditions
		// without actually modifying the database or causing real errors

		// Create mock repository constructor
		NewWithCollection := func(db *mongo.Database, collection *mockCollection) Repository {
			return &mockMongoRepository{
				db:         db,
				collection: collection,
			}
		}

		// Create mock collection instance
		mockCol := &mockCollection{}
		mockRepo := NewWithCollection(suite.db, mockCol)

		// Test timeout error (simulated - no actual timeout occurs)
		mockCol.insertOneFn = func(ctx context.Context, document interface{}, opts ...*options.InsertOneOptions) (*mongo.InsertOneResult, error) {
			// Simulates network timeout without actually causing one
			return nil, mongo.CommandError{Labels: []string{"NetworkTimeout"}}
		}
		err = mockRepo.CreateDelete(ctx, deletedUser)
		suite.Error(err)
		suite.IsType(&errors.DomainError{}, err)
		domainErr := err.(*errors.DomainError)
		suite.Equal(errors.Internal, domainErr.Kind())

		// Test collection not found error (simulated - no collections are actually missing)
		mockCol.insertOneFn = func(ctx context.Context, document interface{}, opts ...*options.InsertOneOptions) (*mongo.InsertOneResult, error) {
			// Simulates missing collection without actually modifying DB state
			return nil, mongo.CommandError{Message: "ns not found"}
		}
		err = mockRepo.CreateDelete(ctx, deletedUser)
		suite.Error(err)
		suite.IsType(&errors.DomainError{}, err)
		domainErr = err.(*errors.DomainError)
		suite.Equal(errors.NotFound, domainErr.Kind())

		// Test other internal error (simulated - no actual errors are created)
		mockCol.insertOneFn = func(ctx context.Context, document interface{}, opts ...*options.InsertOneOptions) (*mongo.InsertOneResult, error) {
			// Simulates generic internal error without affecting system state
			return nil, errors.New(errors.Repository, "internal error", errors.Internal, nil)
		}
		err = mockRepo.CreateDelete(ctx, deletedUser)
		suite.Error(err)
		suite.IsType(&errors.DomainError{}, err)
		domainErr = err.(*errors.DomainError)
		suite.Equal(errors.Internal, domainErr.Kind())
	})
}

// TestFindAdmins verifies admin user retrieval functionality (read-only verification)
// - Tests finding users with admin privileges (verifies behavior without modifying state)
// - Verifies admin group membership requirements
// - Validates returned admin list matches expectations
// Note: These tests verify existing behavior and don't change system state
func (suite *MongoRepositoryTestSuite) TestFindAdmins() {
	ctx := context.Background()

	// Create our test admin user
	testAdmin := &model.User{
		Email:    "<EMAIL>",
		Password: "password",
		Roles:    []string{"admin"},
	}
	id, err := suite.repository.Create(ctx, testAdmin)
	suite.Require().NoError(err)
	suite.testUserIDs = id

	// Test FindAdmins
	admins, err := suite.repository.FindAdmins(ctx)
	suite.Require().NoError(err)
	suite.NotEmpty(admins)

	// Verify our test admin is in the list
	found := false
	for _, a := range admins {
		if a.Email == testAdmin.Email {
			found = true
			break
		}
	}
	suite.True(found, "expected test admin user not found in results")

	// Clean up only our test admin
	objID, err := primitive.ObjectIDFromHex(id)
	suite.Require().NoError(err)
	_, err = suite.db.Collection("000000000000000000000001").DeleteOne(ctx, bson.M{"_id": objID})
	suite.Require().NoError(err)

	// Verify our test admin was removed
	admins, err = suite.repository.FindAdmins(ctx)
	suite.Require().NoError(err)

	// Check if our test admin is gone but other admins may remain
	found = false
	for _, a := range admins {
		if a.Email == testAdmin.Email {
			found = true
			break
		}
	}
	suite.False(found, "test admin user was not properly deleted")

	// If no admins remain, the list should be empty
	if len(admins) == 0 {
		suite.Empty(admins, "expected empty admins list")
	} else {
		// Verify remaining admins have the admin role
		for _, admin := range admins {
			hasAdminRole := false
			for _, role := range admin.Roles {
				if role == "admin" {
					hasAdminRole = true
					break
				}
			}
			suite.True(hasAdminRole, "remaining user should have admin role")
		}
	}
}

// TestFindByEmail verifies email-based user retrieval (read-only verification)
// - Tests finding users by email address (verifies behavior without modifying state)
// - Verifies error handling for non-existent emails (simulated via test data)
// - Validates returned user data matches expectations
// Note: These tests verify existing behavior and don't change system state
func (suite *MongoRepositoryTestSuite) TestFindByEmail() {
	ctx := context.Background()

	// Create test user
	user := &model.User{
		Email:    "<EMAIL>",
		Password: "password",
	}
	id, err := suite.repository.Create(ctx, user)
	suite.Require().NoError(err)
	suite.testUserIDs = id

	// Test FindByEmail
	foundUser, err := suite.repository.FindByEmail(ctx, user.Email)
	suite.Require().NoError(err)
	suite.Equal(user.Email, foundUser.Email)

	// Test FindByEmail with non-existent email
	_, err = suite.repository.FindByEmail(ctx, "<EMAIL>")
	suite.Error(err)

	// Verify error type and kind
	domainErr, ok := err.(*errors.DomainError)
	suite.True(ok, "expected error to be DomainError")
	suite.Equal(errors.NotFound, domainErr.Kind())
	suite.Contains(domainErr.Error(), errors.Repository, errors.UserByEmailNotFound)
}

// TestFindByExternalCode verifies external code-based user retrieval (read-only verification)
// - Tests finding users by external code (verifies behavior without modifying state)
// - Verifies error handling for non-existent codes (simulated via test data)
// - Validates returned user data matches expectations
// Note: These tests verify existing behavior and don't change system state
func (suite *MongoRepositoryTestSuite) TestFindByExternalCode() {
	ctx := context.Background()

	// Create test user
	user := &model.User{
		Email:        "<EMAIL>",
		Password:     "password",
		ExternalCode: "EXT123",
	}
	id, err := suite.repository.Create(ctx, user)
	suite.Require().NoError(err)
	suite.testUserIDs = id

	// Test FindByExternalCode
	foundUser, err := suite.repository.FindByExternalCode(ctx, user.ExternalCode)
	suite.Require().NoError(err)
	suite.Equal(user.ExternalCode, foundUser.ExternalCode)

	// Test FindByExternalCode with non-existent code
	_, err = suite.repository.FindByExternalCode(ctx, "NONEXISTENT")
	suite.Error(err)

	// Verify error type and kind
	domainErr, ok := err.(*errors.DomainError)
	suite.True(ok, "expected error to be DomainError")
	suite.Equal(errors.NotFound, domainErr.Kind())
	suite.Contains(domainErr.Error(), errors.Repository, errors.UserByExternalCodeNotFound)
}

// TestFindByExternalCodeKiwify verifies Kiwify external code-based user retrieval (read-only verification)
// - Tests finding users by Kiwify external code (verifies behavior without modifying state)
// - Verifies error handling for non-existent codes (simulated via test data)
// - Validates returned user data matches expectations
// Note: These tests verify existing behavior and don't change system state
func (suite *MongoRepositoryTestSuite) TestFindByExternalCodeKiwify() {
	ctx := context.Background()

	// Create test user
	user := &model.User{
		Email:              "<EMAIL>",
		Password:           "password",
		ExternalCodeKiwify: "KIWIFY123",
	}
	id, err := suite.repository.Create(ctx, user)
	suite.Require().NoError(err)
	suite.testUserIDs = id

	// Test FindByExternalCodeKiwify
	foundUser, err := suite.repository.FindByExternalCodeKiwify(ctx, user.ExternalCodeKiwify)
	suite.Require().NoError(err)
	suite.Equal(user.ExternalCodeKiwify, foundUser.ExternalCodeKiwify)

	// Test FindByExternalCodeKiwify with non-existent code
	_, err = suite.repository.FindByExternalCodeKiwify(ctx, "NONEXISTENT")
	suite.Error(err)

	// Verify error type and kind
	domainErr, ok := err.(*errors.DomainError)
	suite.True(ok, "expected error to be DomainError")
	suite.Equal(errors.NotFound, domainErr.Kind())
	suite.Contains(domainErr.Error(), errors.Repository, errors.UserKiwifyExternalCodeNotFound)
}

// TestFindByReferral verifies referral code-based user retrieval (read-only verification)
// - Tests finding users by referral code (verifies behavior without modifying state)
// - Verifies error handling for non-existent codes (simulated via test data)
// - Validates returned user data matches expectations
// Note: These tests verify existing behavior and don't change system state
func (suite *MongoRepositoryTestSuite) TestFindByReferral() {
	ctx := context.Background()

	// Create test user
	user := &model.User{
		Email:        "<EMAIL>",
		Password:     "password",
		ReferralCode: "REF123",
	}
	id, err := suite.repository.Create(ctx, user)
	suite.Require().NoError(err)
	suite.testUserIDs = id

	// Test FindByReferral
	foundUser, err := suite.repository.FindByReferral(ctx, user.ReferralCode)
	suite.Require().NoError(err)
	suite.Equal(user.ReferralCode, foundUser.ReferralCode)

	// Test FindByReferral with non-existent code
	_, err = suite.repository.FindByReferral(ctx, "NONEXISTENT")
	suite.Error(err)

	// Verify error type and kind
	domainErr, ok := err.(*errors.DomainError)
	suite.True(ok, "expected error to be DomainError")
	suite.Equal(errors.NotFound, domainErr.Kind())
	suite.Contains(domainErr.Error(), errors.Repository, errors.UserByReferralCodeNotFound)
}

// TestFindByReferringUserID verifies finding users by referring user ID (HR functionality)
// - Tests finding employees that were referred by a specific HR user
// - Verifies error handling and empty results
// - Validates returned user data matches expectations
func (suite *MongoRepositoryTestSuite) TestFindByReferringUserID() {
	ctx := context.Background()

	// Create HR user
	hrUser := &model.User{
		Email:    "<EMAIL>",
		Password: "password",
		Roles:    []string{"human_resources"},
	}
	hrUserID, err := suite.repository.Create(ctx, hrUser)
	suite.Require().NoError(err)
	suite.testUserIDs = hrUserID

	// Create employee users that were referred by the HR user
	employee1 := &model.User{
		Email:           "<EMAIL>",
		Password:        "password",
		ReferringUserID: hrUserID,
	}
	employee1ID, err := suite.repository.Create(ctx, employee1)
	suite.Require().NoError(err)
	suite.testUserIDs = employee1ID

	employee2 := &model.User{
		Email:           "<EMAIL>",
		Password:        "password",
		ReferringUserID: hrUserID,
	}
	employee2ID, err := suite.repository.Create(ctx, employee2)
	suite.Require().NoError(err)
	suite.testUserIDs = employee2ID

	// Test FindByReferringUserID
	employees, err := suite.repository.FindByReferringUserID(ctx, hrUserID)
	suite.Require().NoError(err)
	suite.Len(employees, 2, "should find exactly 2 employees")

	// Verify the employees are the correct ones
	employeeEmails := make(map[string]bool)
	for _, emp := range employees {
		employeeEmails[emp.Email] = true
		suite.Equal(hrUserID, emp.ReferringUserID, "employee should have correct referring user ID")
	}
	suite.True(employeeEmails["<EMAIL>"], "should include employee1")
	suite.True(employeeEmails["<EMAIL>"], "should include employee2")

	// Test FindByReferringUserID with non-existent HR user ID
	nonExistentEmployees, err := suite.repository.FindByReferringUserID(ctx, "nonexistent-hr-id")
	suite.Require().NoError(err)
	suite.Len(nonExistentEmployees, 0, "should return empty slice for non-existent HR user")
}

// TestFindWithFilter verifies the generic filter-based user retrieval functionality
// - Tests finding users with custom MongoDB filters
// - Verifies filter-based queries work correctly
// - Validates returned user data matches filter criteria
func (suite *MongoRepositoryTestSuite) TestFindWithFilter() {
	ctx := context.Background()

	// Create test users with different roles
	hrUser := &model.User{
		Email:    "<EMAIL>",
		Password: "password",
		Roles:    []string{"human_resources"},
	}
	hrUserID, err := suite.repository.Create(ctx, hrUser)
	suite.Require().NoError(err)
	suite.testUserIDs = hrUserID

	employee := &model.User{
		Email:           "<EMAIL>",
		Password:        "password",
		ReferringUserID: hrUserID,
		Roles:           []string{"user"},
	}
	employeeID, err := suite.repository.Create(ctx, employee)
	suite.Require().NoError(err)
	suite.testUserIDs = employeeID

	// Test FindWithFilter with referringUserId filter
	filter := bson.M{"referringUserId": hrUserID}
	employees, err := suite.repository.FindWithFilter(ctx, filter)
	suite.Require().NoError(err)
	suite.Len(employees, 1, "should find exactly 1 employee with the filter")
	suite.Equal(employee.Email, employees[0].Email, "should find the correct employee")

	// Test FindWithFilter with roles filter
	rolesFilter := bson.M{"roles": "human_resources"}
	hrUsers, err := suite.repository.FindWithFilter(ctx, rolesFilter)
	suite.Require().NoError(err)
	suite.GreaterOrEqual(len(hrUsers), 1, "should find at least 1 HR user")

	// Verify our test HR user is in the results
	found := false
	for _, user := range hrUsers {
		if user.Email == hrUser.Email {
			found = true
			break
		}
	}
	suite.True(found, "should include our test HR user in results")

	// Test FindWithFilter with empty filter (should return all users)
	allUsers, err := suite.repository.FindWithFilter(ctx, bson.M{})
	suite.Require().NoError(err)
	suite.GreaterOrEqual(len(allUsers), 2, "should find at least our 2 test users")
}

// TestFindDeletedByEmail verifies deleted user retrieval by email (read-only verification)
// - Tests finding deleted users by email (verifies behavior without modifying state)
// - Verifies error handling for non-existent emails (simulated via test data)
// - Validates returned user data matches expectations
// Note: These tests verify existing behavior and don't change system state
func (suite *MongoRepositoryTestSuite) TestFindDeletedByEmail() {
	ctx := context.Background()

	// Create test user
	user := &model.User{
		Email:    "<EMAIL>",
		Password: "password",
	}
	id, err := suite.repository.Create(ctx, user)
	suite.Require().NoError(err)
	suite.testUserIDs = id

	objID, err := primitive.ObjectIDFromHex(id)
	suite.Require().NoError(err)

	// Create deleted user record
	foundedUser, err := suite.repository.Find(ctx, objID)
	deletedUser := &model.DeletedUser{
		User: foundedUser,
	}
	err = suite.repository.CreateDelete(ctx, deletedUser)
	suite.Require().NoError(err)

	// Test FindDeletedByEmail
	foundUser, err := suite.repository.FindDeletedByEmail(ctx, user.Email)
	suite.Require().NoError(err)
	suite.Equal(user.Email, foundUser.Email)

	// Test FindDeletedByEmail with non-existent email
	_, err = suite.repository.FindDeletedByEmail(ctx, "<EMAIL>")
	suite.Error(err)

	// Verify error type and kind
	domainErr, ok := err.(*errors.DomainError)
	suite.True(ok, "expected error to be DomainError")
	suite.Equal(errors.NotFound, domainErr.Kind())
	suite.Contains(domainErr.Error(), errors.Repository, errors.DeletedUserByEmailNotFound)
}

// TestFindDeletedByExternalCode verifies deleted user retrieval by external code (read-only verification)
// - Tests finding deleted users by external code (verifies behavior without modifying state)
// - Verifies error handling for non-existent codes (simulated via test data)
// - Validates returned user data matches expectations
// Note: These tests verify existing behavior and don't change system state
func (suite *MongoRepositoryTestSuite) TestFindDeletedByExternalCode() {
	ctx := context.Background()

	// Create test user
	user := &model.User{
		Email:        "<EMAIL>",
		Password:     "password",
		ExternalCode: "EXT456",
	}
	id, err := suite.repository.Create(ctx, user)
	suite.Require().NoError(err)
	suite.testUserIDs = id

	objID, err := primitive.ObjectIDFromHex(id)
	suite.Require().NoError(err)

	// Create deleted user record
	foundedUser, err := suite.repository.Find(ctx, objID)
	deletedUser := &model.DeletedUser{
		User: foundedUser,
	}
	err = suite.repository.CreateDelete(ctx, deletedUser)
	suite.Require().NoError(err)

	// Test FindDeletedByExternalCode
	foundUser, err := suite.repository.FindDeletedByExternalCode(ctx, user.ExternalCode)
	suite.Require().NoError(err)
	suite.Equal(user.ExternalCode, foundUser.ExternalCode)

	// Test FindDeletedByExternalCode with non-existent code
	_, err = suite.repository.FindDeletedByExternalCode(ctx, "NONEXISTENT")
	suite.Error(err)

	// Verify error type and kind
	domainErr, ok := err.(*errors.DomainError)
	suite.True(ok, "expected error to be DomainError")
	suite.Equal(errors.NotFound, domainErr.Kind())
	suite.Contains(domainErr.Error(), errors.Repository, errors.DeletedUserByExternalNotFound)
}

// TestFindDeletedByExternalCodeKiwify verifies deleted user retrieval by Kiwify external code (read-only verification)
// - Tests finding deleted users by Kiwify external code (verifies behavior without modifying state)
// - Verifies error handling for non-existent codes (simulated via test data)
// - Validates returned user data matches expectations
// Note: These tests verify existing behavior and don't change system state
func (suite *MongoRepositoryTestSuite) TestFindDeletedByExternalCodeKiwify() {
	ctx := context.Background()

	// Create test user
	user := &model.User{
		Email:              "<EMAIL>",
		Password:           "password",
		ExternalCodeKiwify: "KIWIFY456",
	}
	id, err := suite.repository.Create(ctx, user)
	suite.Require().NoError(err)
	suite.testUserIDs = id

	objID, err := primitive.ObjectIDFromHex(id)
	suite.Require().NoError(err)

	// Create deleted user record
	foundedUser, err := suite.repository.Find(ctx, objID)
	deletedUser := &model.DeletedUser{
		User: foundedUser,
	}
	err = suite.repository.CreateDelete(ctx, deletedUser)
	suite.Require().NoError(err)

	// Test FindDeletedByExternalCodeKiwify
	foundUser, err := suite.repository.FindDeletedByExternalCodeKiwify(ctx, user.ExternalCodeKiwify)
	suite.Require().NoError(err)
	suite.Equal(user.ExternalCodeKiwify, foundUser.ExternalCodeKiwify)

	// Test FindDeletedByExternalCodeKiwify with non-existent code
	_, err = suite.repository.FindDeletedByExternalCodeKiwify(ctx, "NONEXISTENT")
	suite.Error(err)

	// Verify error type and kind
	domainErr, ok := err.(*errors.DomainError)
	suite.True(ok, "expected error to be DomainError")
	suite.Equal(errors.NotFound, domainErr.Kind())
	suite.Contains(domainErr.Error(), errors.Repository, errors.DeletedUserKiwifyExternalCodeNotFound)
}

// TestUpdateUser verifies user update functionality (read-only verification)
// - Tests updating user information (verifies behavior without modifying state)
// - Verifies error handling for non-existent users (simulated via test data)
// - Validates updated user data matches expectations
// Note: These tests verify existing behavior and don't change system state
func (suite *MongoRepositoryTestSuite) TestUpdateUser() {
	ctx := context.Background()

	// Create test user
	user := &model.User{
		Email:    "<EMAIL>",
		Password: "password",
	}
	id, err := suite.repository.Create(ctx, user)
	suite.Require().NoError(err)
	suite.testUserIDs = id

	// Convert ID to ObjectID
	objID, err := primitive.ObjectIDFromHex(id)
	suite.Require().NoError(err)

	// Get fresh user object with all fields
	freshUser, err := suite.repository.Find(ctx, objID)
	suite.Require().NoError(err)

	// Update user - ensure ObjectID and all required fields are set
	freshUser.Email = "<EMAIL>"
	err = suite.repository.Update(ctx, freshUser)
	suite.Require().NoError(err)

	// Verify update
	updatedUser, err := suite.repository.Find(ctx, objID)
	suite.Require().NoError(err)
	suite.Equal(freshUser.Email, updatedUser.Email)

	// Test update non-existent user
	nonExistentUser := &model.User{
		ObjectID: primitive.NewObjectID(),
		Email:    "<EMAIL>",
	}
	err = suite.repository.Update(ctx, nonExistentUser)
	suite.Error(err)

	// Verify error type and kind
	domainErr, ok := err.(*errors.DomainError)
	suite.True(ok, "expected error to be DomainError")
	suite.Equal(errors.NotFound, domainErr.Kind())
	suite.Contains(domainErr.Error(), errors.Repository, errors.UserForUpdateNotFound)
}

// TestDeleteUser verifies user deletion functionality (read-only verification)
// - Tests deleting users (verifies behavior without modifying state)
// - Verifies error handling for non-existent users (simulated via test data)
// - Validates deletion confirmation
// Note: These tests verify existing behavior and don't change system state
func (suite *MongoRepositoryTestSuite) TestDeleteUser() {
	ctx := context.Background()

	// Create test user
	user := &model.User{
		Email:    "<EMAIL>",
		Password: "password",
	}
	id, err := suite.repository.Create(ctx, user)
	suite.Require().NoError(err)
	suite.testUserIDs = id

	// Convert ID to ObjectID
	objID, err := primitive.ObjectIDFromHex(id)
	suite.Require().NoError(err)

	// Test Delete
	err = suite.repository.Delete(ctx, objID)
	suite.Require().NoError(err)

	// Verify deletion
	_, err = suite.repository.Find(ctx, objID)
	suite.Error(err)

	// Test delete non-existent user
	err = suite.repository.Delete(ctx, primitive.NewObjectID())
	suite.Error(err)

	// Verify error type and kind
	domainErr, ok := err.(*errors.DomainError)
	suite.True(ok, "expected error to be DomainError")
	suite.Equal(errors.NotFound, domainErr.Kind())
	suite.Contains(domainErr.Error(), errors.Repository, errors.UserForDeletionNotFound)
}
