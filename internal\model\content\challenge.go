package content

import (
	"fmt"
	"strings"
)

type Challenge struct {
	Name        string            `json:"name" bson:"name"`
	Identifier  string            `json:"identifier" bson:"identifier"`
	Description string            `json:"description" bson:"description"`
	Logo        string            `json:"logo" bson:"logo"`
	Color       string            `json:"color" bson:"color"`
	Phases      []*ChallengePhase `json:"phases" bson:"phases"`
	Locked      bool              `json:"locked" bson:"locked"`
}

type ChallengePhase struct {
	Name         string               `json:"name" bson:"name"`
	Identifier   string               `json:"identifier" bson:"identifier"`
	Description  string               `json:"description" bson:"description"`
	Requirements []string             `json:"requirements" bson:"requirements"`
	Order        uint8                `json:"order" bson:"order"`
	Logo         string               `json:"logo" bson:"logo"`
	Color        string               `json:"color" bson:"color"`
	Advertising  ChallengeAdvertising `json:"advertising" bson:"advertising"`
	Content      []*ChallengeContent  `json:"content" bson:"content"`
}

type ChallengeContent struct {
	Image       string             `json:"image,omitempty" bson:"image,omitempty"`
	Identifier  string             `json:"identifier" bson:"identifier"`
	Description string             `json:"description" bson:"description"`
	Next        string             `json:"next,omitempty" bson:"next,omitempty"`
	Choices     []*ChallengeChoice `json:"choices,omitempty" bson:"choices,omitempty"`
}

type ChallengeChoice struct {
	Name       string             `json:"name"`
	Identifier string             `json:"identifier"`
	Type       string             `json:"type"`
	Next       string             `json:"next,omitempty"`
	Points     int                `json:"points,omitempty" bson:"points,omitempty"`
	Choices    []*ChallengeChoice `json:"choices,omitempty"`
}

type ChallengeAdvertising struct {
	Banner    string `json:"banner" bson:"banner"`
	BannerURL string `json:"bannerURL" bson:"bannerURL"`
}

func (c *Challenge) GetPhase(identifier string) *ChallengePhase {
	for _, phase := range c.Phases {
		if phase.Identifier == identifier {
			return phase
		}
	}
	return nil
}

func (cp *ChallengePhase) GetContent(identifier string) *ChallengeContent {
	for _, content := range cp.Content {
		if content.Identifier == identifier {
			return content
		}
	}
	return nil
}

func (cp *ChallengePhase) GetNext(identifier string, choice string) string {

	for _, content := range cp.Content {
		if content.Identifier == identifier {
			if content.Next != "" {
				return content.Next
			}
			for _, ch := range content.Choices {
				next := ch.GetNext(choice)
				if next != "" {
					return next
				}
			}
		}
	}

	return ""
}

func (cp *ChallengePhase) GetPoints(identifier string, choice string) int {
	for _, content := range cp.Content {
		if content.Identifier == identifier {
			for _, ch := range content.Choices {
				points := ch.GetPoints(choice)
				if points > 0 {
					return points
				}
			}
		}
	}

	return 0
}

func (cch *ChallengeChoice) GetNext(choice string) string {

	if cch.Identifier == choice {
		return cch.Next
	}

	for _, ch := range cch.Choices {
		next := ch.GetNext(choice)
		if next != "" {
			return next
		}
	}

	return ""
}

func (cch *ChallengeChoice) GetPoints(choice string) int {

	if cch.Identifier == choice {
		return cch.Points
	}

	for _, ch := range cch.Choices {
		points := ch.GetPoints(choice)
		if points > 0 {
			return points
		}
	}

	return 0
}

func (c *Challenge) ValidateCreate() error {
	if c.Name == "" {
		return ErrChallengeRequiredName
	}

	if c.Identifier == "" {
		return ErrChallengeRequiredIdentifier
	}

	if c.Logo == "" {
		return ErrChallengeRequiredLogo
	}

	if c.Phases == nil {
		return ErrChallengeRequiredPhases
	}

	return nil
}

func (c *Challenge) PrepareCreate() error {
	c.Name = strings.TrimSpace(c.Name)
	c.Logo = strings.TrimSpace(c.Logo)

	if err := c.ValidateCreate(); err != nil {
		return err
	}

	if c.Phases != nil {
		for _, phase := range c.Phases {
			if err := phase.PrepareCreate(); err != nil {
				return err
			}
		}
	}

	return nil
}

func (cp *ChallengePhase) ValidateCreate() error {
	if cp.Name == "" {
		return ErrChallengePhaseRequiredName
	}

	if cp.Identifier == "" {
		return ErrChallengePhaseRequiredIdentifier
	}

	if cp.Logo == "" {
		return ErrChallengePhaseRequiredLogo
	}

	if cp.Content == nil {
		return ErrChallengePhaseRequiredContent
	}

	if cp.Requirements == nil {
		return ErrChallengePhaseRequiredRequirement
	}

	// Validates if there is two content with same identifier.
	uniqueKeys := map[string]bool{
		"coin":    true,
		"diamond": true,
		"free":    true,
	}

	for _, content := range cp.Content {
		if content != nil && content.Identifier != "" {
			if uniqueKeys[content.Identifier] {
				return ErrChallengePhaseContentIdentifierShouldBeUnique
			} else {
				uniqueKeys[content.Identifier] = true
			}
		}
	}

	for _, phase := range cp.Content {
		if phase != nil && phase.Identifier != "" {
			if phase.Next != "" {
				if _, ok := uniqueKeys[phase.Next]; !ok {
					return fmt.Errorf("%w: %s", ErrChallengeContentOrChallengeChoiceNextInvalidValue, phase.Next)
				} else if phase.Next == phase.Identifier {
					return fmt.Errorf("%w: %s != %s", ErrChallengeContentOrChallengeChoiceNextInvalidValue, phase.Next, phase.Identifier)
				}
			} else {
				if phase.Choices != nil && len(phase.Choices) > 0 {
					for _, choice := range phase.Choices {
						if choice.Next != "" {
							if _, ok := uniqueKeys[choice.Next]; !ok || choice.Next == phase.Identifier {
								return fmt.Errorf("%w: %s != %s", ErrChallengeContentOrChallengeChoiceNextInvalidValue, choice.Next, phase.Identifier)
							}
						}
					}
				} else {
					return ErrChallengeContentOrChallengeChoiceShouldHaveNext
				}
			}
		}
	}

	return nil
}

func (cp *ChallengePhase) PrepareCreate() error {
	cp.Name = strings.TrimSpace(cp.Name)
	cp.Logo = strings.TrimSpace(cp.Logo)

	if err := cp.ValidateCreate(); err != nil {
		return err
	}

	if cp.Content != nil {
		for _, phase := range cp.Content {
			if err := phase.PrepareCreate(); err != nil {
				return err
			}
		}
	}

	return nil
}

func (cc *ChallengeContent) ValidateCreate() error {
	if cc.Description == "" {
		return ErrChallengeContentRequiredDescription
	}

	if cc.Identifier == "" {
		return ErrChallengeContentRequiredIdentifier
	}

	if cc.Next == "" {
		isValid := true
		visitedChoices := map[string]bool{}
		for _, cch := range cc.Choices {
			isValid = isValid && isChallengeChoiceWithValidNext(cch, visitedChoices, 0)
			if !isValid {
				return ErrLessonContentOrLessonChoiceShouldHaveNext
			}
		}
	}
	return nil
}

func isChallengeChoiceWithValidNext(choice *ChallengeChoice, visitedChoices map[string]bool, depth uint8) bool {
	visitedChoices[fmt.Sprintf("%s-%d", choice.Identifier, depth)] = true
	if choice.Next == "" && choice.Choices != nil && len(choice.Choices) > 0 {
		isValid := true
		for _, choicesChoice := range choice.Choices {
			if _, ok := visitedChoices[choicesChoice.Identifier]; !ok {
				isValid = isValid && isChallengeChoiceWithValidNext(choicesChoice, visitedChoices, depth+1)
				if !isValid {
					return false
				}
			}
		}
	} else if choice.Next == "" {
		return false
	}

	return true
}

func (cc *ChallengeContent) PrepareCreate() error {
	cc.Image = strings.TrimSpace(cc.Image)
	cc.Description = strings.TrimSpace(cc.Description)
	cc.Identifier = strings.ToLower(strings.TrimSpace(cc.Identifier))

	if err := cc.ValidateCreate(); err != nil {
		return err
	}

	for _, choice := range cc.Choices {
		if err := choice.PrepareCreate(); err != nil {
			return err
		}
	}

	return nil
}

func (cch *ChallengeChoice) ValidateCreate() error {
	if cch.Name == "" {
		return ErrChallengeChoiceRequiredName
	}

	if cch.Identifier == "" {
		return ErrChallengeChoiceRequiredIdentifier
	}

	validChoiceTypes := []string{"TEXT", "NUMBER", "OPTIONS", "DATE"}
	isValid := false
	for _, choiceType := range validChoiceTypes {
		if choiceType == cch.Type {
			isValid = true
		}
	}

	if !isValid {
		return ErrChallengeChoiceTypeInvalid
	}

	return nil
}

func (cch *ChallengeChoice) PrepareCreate() error {
	cch.Name = strings.TrimSpace(cch.Name)
	cch.Identifier = strings.TrimSpace(cch.Identifier)
	cch.Type = strings.ToUpper(strings.TrimSpace(cch.Type))

	if err := cch.ValidateCreate(); err != nil {
		return err
	}

	return nil
}

/////// UPDATE

func (c *Challenge) ValidateUpdate() error {
	if err := c.ValidateCreate(); err != nil {
		return err
	}

	if c.Phases != nil {
		for _, phase := range c.Phases {
			if err := phase.PrepareUpdate(); err != nil {
				return err
			}
		}
	}
	return nil
}

func (c *Challenge) PrepareUpdate() error {
	return c.ValidateUpdate()
}

func (cp *ChallengePhase) ValidateUpdate() error {
	if err := cp.ValidateCreate(); err != nil {
		return err
	}

	if cp.Content != nil {
		for _, content := range cp.Content {
			if err := content.PrepareUpdate(); err != nil {
				return err
			}
		}
	}
	return nil
}

func (cp *ChallengePhase) PrepareUpdate() error {
	return cp.ValidateUpdate()
}

func (cc *ChallengeContent) ValidateUpdate() error {
	return cc.ValidateCreate()

}

func (cc *ChallengeContent) PrepareUpdate() error {
	if err := cc.ValidateUpdate(); err != nil {
		return err
	}

	for _, choice := range cc.Choices {
		if err := choice.PrepareUpdate(); err != nil {
			return err
		}
	}

	return nil
}

func (cch *ChallengeChoice) ValidateUpdate() error {
	return cch.ValidateCreate()
}

func (cch *ChallengeChoice) PrepareUpdate() error {
	if err := cch.ValidateUpdate(); err != nil {
		return err
	}

	return nil
}
