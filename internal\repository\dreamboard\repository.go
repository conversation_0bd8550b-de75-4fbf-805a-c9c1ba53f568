package dreamboard

import (
	"context"

	"github.com/dsoplabs/dinbora-backend/internal/model/dreamboard"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

type Reader interface {
	// Read operations
	Find(ctx context.Context, id primitive.ObjectID) (*dreamboard.Dreamboard, error)
	FindAll(ctx context.Context) ([]*dreamboard.Dreamboard, error)
	FindByUser(ctx context.Context, userID string) (*dreamboard.Dreamboard, error)
	FindByDreamID(ctx context.Context, dreamID primitive.ObjectID) (*dreamboard.Dreamboard, error)

	// Category Management
	FindCategory(ctx context.Context, boardID primitive.ObjectID, categoryID primitive.ObjectID) (*dreamboard.Category, error)
	// Dream Management
	FindDream(ctx context.Context, boardID primitive.ObjectID, dreamID primitive.ObjectID) (*dreamboard.Dream, error)

	// Share Link Management
	FindShareLink(ctx context.Context, id primitive.ObjectID) (*dreamboard.ShareLink, error)
	FindShareLinkByCode(ctx context.Context, code string) (*dreamboard.ShareLink, error)
	FindShareLinkByDreamID(ctx context.Context, dreamID string) (*dreamboard.ShareLink, error)

	// Contribution Management
	FindContribution(ctx context.Context, id primitive.ObjectID) (*dreamboard.Contribution, error)
	FindContributionsByDreamID(ctx context.Context, dreamID string) ([]*dreamboard.Contribution, error)
	FindContributionsByUserID(ctx context.Context, userID string) ([]*dreamboard.Contribution, error)
	FindContributionByDreamAndUser(ctx context.Context, dreamID, userID string) (*dreamboard.Contribution, error)
	FindActiveContributionsByDreamID(ctx context.Context, dreamID string) ([]*dreamboard.Contribution, error)
}

type Writer interface {
	// Create operations
	Create(ctx context.Context, dreamboard *dreamboard.Dreamboard) (string, error)
	CreateDelete(ctx context.Context, deletedDreamboard *dreamboard.DeletedDreamboard) error

	// Update operations
	Update(ctx context.Context, dreamboard *dreamboard.Dreamboard) error

	// Delete operations
	Delete(ctx context.Context, id primitive.ObjectID) error

	// Category Management
	CreateCategory(ctx context.Context, boardID primitive.ObjectID, category *dreamboard.Category) error
	CreateCategories(ctx context.Context, boardID primitive.ObjectID, categories []dreamboard.Category) error
	UpdateCategory(ctx context.Context, boardID primitive.ObjectID, category *dreamboard.Category) error
	DeleteCategory(ctx context.Context, boardID primitive.ObjectID, categoryID primitive.ObjectID) error
	// Dream Management
	CreateDream(ctx context.Context, boardID primitive.ObjectID, dream *dreamboard.Dream) error
	UpdateDream(ctx context.Context, boardID primitive.ObjectID, dream *dreamboard.Dream) error
	RemoveDream(ctx context.Context, boardID primitive.ObjectID, dreamID primitive.ObjectID) error

	// Share Link Management
	CreateShareLink(ctx context.Context, shareLink *dreamboard.ShareLink) (string, error)
	UpdateShareLink(ctx context.Context, shareLink *dreamboard.ShareLink) error
	DeleteShareLink(ctx context.Context, id primitive.ObjectID) error

	// Contribution Management
	CreateContribution(ctx context.Context, contribution *dreamboard.Contribution) (string, error)
	UpdateContribution(ctx context.Context, contribution *dreamboard.Contribution) error
	UpdateContributionStatusByDreamID(ctx context.Context, dreamID string, status dreamboard.ContributionStatus) error
	DeleteContribution(ctx context.Context, id primitive.ObjectID) error
}

type Repository interface {
	Reader
	Writer
}
