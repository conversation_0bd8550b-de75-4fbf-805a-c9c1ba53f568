## 1. Visão Geral

O sistema é uma aplicação web e app para gestão financeira pessoal que permite aos usuários registrar e acompanhar suas movimentações financeiras de forma organizada e intuitiva.

## 2. Estrutura de Categorização

### 2.1 Ganhos

Registros de entrada de recursos financeiros, divididos em:

#### 2.1.1 Remuneração

- **Objetivo**: Registrar ganhos provenientes de trabalho
- **Subcategorias**:
    - Salário CLT
    - Contrato PJ
    - Pró-labore
    - Comissões
    - Bônus
    - Outras remunerações

#### 2.1.2 Benefícios

- **Objetivo**: Registrar ganhos provenientes de benefícios
- **Subcategorias**:
    - Aposentadoria
    - Bolsa Família
    - Pensão
    - Vale Refeição
    - Vale Transporte
    - Outros benefícios

#### 2.1.3 Renda de Investimentos

- **Objetivo**: Registrar ganhos provenientes de investimentos
- **Subcategorias**:
    - Dividendos
    - Juros
    - Resgate
    - Renda de Aluguel
    - Restituição
    - Outras rendas

#### 2.1.4 Ganhos Adicionais

- **Objetivo**: Registrar ganhos diversos não regulares
- **Subcategorias**:
    - Venda de bens
    - Monetização de redes sociais
    - Cashback
    - Prêmios
    - Herança
    - Outros ganhos

### 2.2 Custos de Vida

Registros de despesas essenciais e planejamento financeiro:

#### 2.2.1 Necessidades

- **Objetivo**: Registrar gastos essenciais
- **Subcategorias**:
    - Dízimo
    - Doação
    - Roupas
    - Móveis
    - Eletrodomésticos
    - Outras necessidades

#### 2.2.2 Sonhos

- **Objetivo**: Registrar objetivos financeiros
- **Subcategorias**:
    - Curto prazo
    - Médio prazo
    - Longo prazo
    - Outros sonhos

#### 2.2.3 Dívidas

- **Objetivo**: Registrar pagamento de dívidas
- **Subcategorias**:
    - Financiamento Imobiliário
    - Financiamento Automóvel
    - Cartão de Crédito
    - Cheque Especial
    - Crédito Pessoal
    - Outras dívidas

#### 2.2.4 Reservas

- **Objetivo**: Registrar reservas financeiras
- **Subcategorias**:
    - Aquisição Patrimonial
    - Previdência
    - Reserva Estratégica
    - Investimentos
    - Seguro de Vida
    - Outras reservas

### 2.3 Gastos Gerais

Registros de despesas do dia a dia:

#### 2.3.1 Moradia

- **Objetivo**: Registrar despesas relacionadas à moradia
- **Subcategorias**:
    - Aluguel ou Prestação
    - Condomínio
    - Manutenção
    - Seguro Residencial
    - Outras despesas de moradia

#### 2.3.2 Contas de Casa

- **Objetivo**: Registrar despesas com serviços essenciais
- **Subcategorias**:
    - Conta de Água
    - Conta de Luz
    - Conta de Gás
    - Taxas de Coleta
    - Outras contas

## 3. Funcionalidades Principais

### 3.1 Registro de Transações

- **Fluxo de Registro**:
    1. Seleção da categoria
    2. Inserção do valor
    3. Seleção da data
    4. Escolha do meio de pagamento/recebimento
- **Meios de Pagamento**:
    - Para Ganhos:
        - Transferência bancária
        - Dinheiro
        - PIX
        - Cartão de crédito
        - Cheque
        - Outros
    - Para Despesas:
        - Dinheiro
        - PIX
        - Cartão de crédito
        - Cartão de débito
        - Boleto
        - Outros

### 3.2 Sistema de Gamificação

#### 3.2.1 Ligas
O sistema de ligas funciona de maneira que estimula o usuário a preencher seus ganhos e gastos todos os dias, dessa forma ele conquista dias de “Investida”, quanto mais investidas, maior a posição no Ranking da Liga. (Considerar que as ligas podem ter temporadas ou não)

- **Níveis**:
    1. Bronze (0-29 dias)
    2. Prata (30-59 dias)
    3. Ouro (60-89 dias)
    4. Diamante (90+ dias)
- **Progressão**:
    - Baseada em dias consecutivos de registro
    - Retorno à liga Bronze ao perder sequência
    - Progresso visual por liga

#### 3.2.2 Temporadas

- **Duração**: TBD
- **Períodos**:
	TBD
- **Características**:
    - Progresso sazonal
    - Recompensas específicas por temporada
    - Ranking de temporada

#### 3.2.3 Conquistas 

- **Tipos**:
- Ao finalizar o desafio “Apontamento Financeiro” e o desafio “Planejamento Financeiro” através da Planilha o usuário deve receber uma conquista.
- Preciso de mais detalhamento.

### 3.3 Visualização e Análise

#### 3.3.1 Resumo Financeiro

- **Cards de Resumo**:
    1. Ganhos Totais
    2. Custos de Vida
    3. Gastos Gerais
    4. Saldo Final
- **Características**:
    - Atualização em tempo real
    - Visualização mensal
    - Detalhamento por categoria

#### 3.3.2 Gráficos e Relatórios (Implementação futura)

- **Tipos de Visualização**:
    - Distribuição de gastos
    - Evolução temporal
    - Comparativo entre categorias
    - Análise de tendências

## 4. Regras de Negócio

### 4.1 Registro de Transações

1. Toda transação deve ter:
    - Categoria
    - Valor
    - Data ?
    - Meio de pagamento/recebimento
2. Não permitir:
    - Valores negativos
    - Datas futuras
    - Categorias em branco

### 4.2 Sistema de Sequência

1. Sequência é quebrada se:
    - Usuário não registrar transações por mais de 1 dia

### 4.3 Cálculo de Saldo

1. Saldo = Ganhos - (Custos de Vida + Gastos Gerais)
2. Atualização automática a cada nova transação
3. Cálculos sempre em duas casas decimais

### 4.4 Ranking e Competição

4. Posição baseada em:
    - Dias consecutivos de registro
    - Total de transações registradas
    - Conquistas desbloqueadas
5. Atualização em tempo real
6. Visibilidade pública do ranking

## 5. Requisitos Técnicos

### 5.1 Performance

- Atualização em tempo real
- Otimização de imagens e recursos - Digital Ocean Spaces

### 5.2 Segurança

- Autenticação obrigatória
- Criptografia de dados sensíveis (Detalhamento)
- Proteção contra XSS e CSRF
- Backup automático de dados (Detalhamento, para o futuro)

### 5.3 Usabilidade

- Interface responsiva
- Suporte a temas claro/escuro (Detalhamento implementação futura)
- Feedback visual de ações

### 5.4 Compatibilidade

- Navegadores modernos
- Dispositivos móveis

## 6. Fluxos de Usuário

### 6.1 Onboarding

7. Cadastro/Login
8. Tutorial interativo
9. Primeira transação guiada
10. Apresentação do sistema de ligas

### 6.2 Uso Diário

11. Visualização do resumo
12. Registro de transações
13. Verificação de metas
14. Acompanhamento de conquistas

### 6.3 Análise Mensal

15. Fechamento do mês
16. Relatório de desempenho - (Implementação futura Detalhamento)
17. Comparativo com meses anteriores - (Implementação futura Detalhamento)
18. Planejamento do próximo mês

## 7. Métricas de Sucesso

### 7.1 Engajamento

- Taxa de retenção diária
- Sequência média de dias
- Transações por usuário
- Tempo de sessão -> (Implementação futura Analytics Detalhamento)

### 7.2 Financeiras

- Total de transações
- Volume financeiro
- Distribuição por categoria - (Implementação futura Detalhamento)
- Taxa de economia - (Implementação futura Detalhamento)

### 7.3 Gamificação

- Progressão nas ligas
- Conquistas desbloqueadas
- Participação no ranking
- Competitividade entre usuários

