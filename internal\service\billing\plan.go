package billing

import (
	"context"

	"github.com/dsoplabs/dinbora-backend/internal/errors"
	"github.com/dsoplabs/dinbora-backend/internal/model/billing"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// Plan CRUD operations
func (s *service) CreatePlan(ctx context.Context, plan *billing.Plan) (string, error) {
	// Set defaults and validate
	plan.SetDefaults()
	if err := plan.Validate(); err != nil {
		return "", err
	}

	// Check for duplicate external product IDs
	if plan.HotmartProductID != "" {
		existing, err := s.repo.Plans().FindByHotmartProductID(ctx, plan.HotmartProductID)
		if err == nil && existing != nil {
			return "", errors.New(errors.Service, "plan with this Hotmart product ID already exists", errors.Conflict, nil)
		}
	}

	if plan.AppleProductID != "" {
		existing, err := s.repo.Plans().FindByAppleProductID(ctx, plan.AppleProductID)
		if err == nil && existing != nil {
			return "", errors.New(errors.Service, "plan with this Apple product ID already exists", errors.Conflict, nil)
		}
	}

	return s.repo.Plans().Create(ctx, plan)
}

func (s *service) FindPlan(ctx context.Context, id primitive.ObjectID) (*billing.Plan, error) {
	return s.repo.Plans().Find(ctx, id)
}

func (s *service) FindAllPlans(ctx context.Context) ([]*billing.Plan, error) {
	return s.repo.Plans().FindAll(ctx)
}

func (s *service) FindActivePlans(ctx context.Context) ([]*billing.Plan, error) {
	return s.repo.Plans().FindActive(ctx)
}

func (s *service) UpdatePlan(ctx context.Context, plan *billing.Plan) error {
	// Validate the plan
	if err := plan.Validate(); err != nil {
		return err
	}

	// Check if plan exists
	existing, err := s.repo.Plans().Find(ctx, plan.ObjectID)
	if err != nil {
		return err
	}

	// Check for duplicate external product IDs (excluding current plan)
	if plan.HotmartProductID != "" && plan.HotmartProductID != existing.HotmartProductID {
		duplicate, err := s.repo.Plans().FindByHotmartProductID(ctx, plan.HotmartProductID)
		if err == nil && duplicate != nil && duplicate.ObjectID != plan.ObjectID {
			return errors.New(errors.Service, "plan with this Hotmart product ID already exists", errors.Conflict, nil)
		}
	}

	if plan.AppleProductID != "" && plan.AppleProductID != existing.AppleProductID {
		duplicate, err := s.repo.Plans().FindByAppleProductID(ctx, plan.AppleProductID)
		if err == nil && duplicate != nil && duplicate.ObjectID != plan.ObjectID {
			return errors.New(errors.Service, "plan with this Apple product ID already exists", errors.Conflict, nil)
		}
	}

	return s.repo.Plans().Update(ctx, plan)
}

func (s *service) DeletePlan(ctx context.Context, id primitive.ObjectID) error {
	// Check if plan exists
	plan, err := s.repo.Plans().Find(ctx, id)
	if err != nil {
		return err
	}

	// Check if plan has active subscriptions
	activeSubscriptions, err := s.repo.Subscriptions().FindByStatus(ctx, billing.SubscriptionStatusActive)
	if err != nil {
		return errors.New(errors.Service, "failed to check for active subscriptions", errors.Internal, err)
	}

	for _, subscription := range activeSubscriptions {
		if subscription.PlanID == id {
			return errors.New(errors.Service, "cannot delete plan with active subscriptions", errors.Conflict, nil)
		}
	}

	// Check if plan has trial subscriptions
	trialSubscriptions, err := s.repo.Subscriptions().FindByStatus(ctx, billing.SubscriptionStatusTrial)
	if err != nil {
		return errors.New(errors.Service, "failed to check for trial subscriptions", errors.Internal, err)
	}

	for _, subscription := range trialSubscriptions {
		if subscription.PlanID == id {
			return errors.New(errors.Service, "cannot delete plan with trial subscriptions", errors.Conflict, nil)
		}
	}

	// Archive the plan instead of deleting it if it has any historical subscriptions
	allSubscriptions, err := s.repo.Subscriptions().FindByStatus(ctx, billing.SubscriptionStatusExpired)
	if err == nil {
		for _, subscription := range allSubscriptions {
			if subscription.PlanID == id {
				// Archive instead of delete
				plan.Status = billing.PlanStatusArchived
				return s.repo.Plans().Update(ctx, plan)
			}
		}
	}

	// Safe to delete if no subscriptions exist
	return s.repo.Plans().Delete(ctx, id)
}

// Plan lookup operations
func (s *service) FindPlanByHotmartProductID(ctx context.Context, hotmartProductID string) (*billing.Plan, error) {
	if hotmartProductID == "" {
		return nil, errors.New(errors.Service, "hotmart product ID is required", errors.Validation, nil)
	}

	return s.repo.Plans().FindByHotmartProductID(ctx, hotmartProductID)
}

func (s *service) FindPlanByAppleProductID(ctx context.Context, appleProductID string) (*billing.Plan, error) {
	if appleProductID == "" {
		return nil, errors.New(errors.Service, "apple product ID is required", errors.Validation, nil)
	}

	return s.repo.Plans().FindByAppleProductID(ctx, appleProductID)
}

// Helper methods for plan management

// ValidatePlanForSubscription validates that a plan can be used for creating a subscription
func (s *service) ValidatePlanForSubscription(ctx context.Context, planID primitive.ObjectID) (*billing.Plan, error) {
	plan, err := s.repo.Plans().Find(ctx, planID)
	if err != nil {
		return nil, err
	}

	if !plan.IsActive() {
		return nil, errors.New(errors.Service, "plan is not active", errors.Validation, nil)
	}

	return plan, nil
}

// GetPlanFeatures returns the features available in a plan
func (s *service) GetPlanFeatures(ctx context.Context, planID primitive.ObjectID) ([]string, error) {
	plan, err := s.repo.Plans().Find(ctx, planID)
	if err != nil {
		return nil, err
	}

	return plan.Features, nil
}

// GetPlanPermissions returns the permissions available in a plan
func (s *service) GetPlanPermissions(ctx context.Context, planID primitive.ObjectID) ([]string, error) {
	plan, err := s.repo.Plans().Find(ctx, planID)
	if err != nil {
		return nil, err
	}

	return plan.Permissions, nil
}

// CheckPlanFeature checks if a plan has a specific feature
func (s *service) CheckPlanFeature(ctx context.Context, planID primitive.ObjectID, feature string) (bool, error) {
	plan, err := s.repo.Plans().Find(ctx, planID)
	if err != nil {
		return false, err
	}

	return plan.HasFeature(feature), nil
}

// CheckPlanPermission checks if a plan has a specific permission
func (s *service) CheckPlanPermission(ctx context.Context, planID primitive.ObjectID, permission string) (bool, error) {
	plan, err := s.repo.Plans().Find(ctx, planID)
	if err != nil {
		return false, err
	}

	return plan.HasPermission(permission), nil
}
