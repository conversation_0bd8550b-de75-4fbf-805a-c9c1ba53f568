package apple

import (
	"context"
	"net/http"

	"github.com/dsoplabs/dinbora-backend/internal/errors"
	"github.com/dsoplabs/dinbora-backend/internal/model"
	"github.com/dsoplabs/dinbora-backend/internal/service/apple"
	"github.com/dsoplabs/dinbora-backend/internal/service/user"
	"github.com/labstack/echo/v4"
)

type Controller interface {
	// Routes
	RegisterRoutes(ctx context.Context, legacyGroup *echo.Group, currentGroup *echo.Group)

	// OAuth
	HandleAppleRegister() echo.HandlerFunc
	HandleAppleLogin() echo.HandlerFunc
}

type controller struct {
	Service apple.Service
}

func New(service user.Service) Controller {
	return &controller{
		Service: apple.New(service),
	}
}

// Routes
func (ac *controller) RegisterRoutes(ctx context.Context, legacyGroup *echo.Group, currentGroup *echo.Group) {
	authGroup := legacyGroup.Group("auth/")
	authGroup.POST("oauth2/callback/apple/register/", ac.HandleAppleRegister())
	authGroup.POST("oauth2/callback/apple/login/", ac.HandleAppleLogin())
}

// OAuth
func (ac *controller) HandleAppleRegister() echo.HandlerFunc {
	return func(c echo.Context) error {
		var callbackRequest struct {
			Access       string            `json:"access"`
			Name         string            `json:"name"`
			Onboarding   *model.Onboarding `json:"onboarding"`
			ReferralCode string            `json:"referral"`
		}

		if err := c.Bind(&callbackRequest); err != nil {
			//http.Redirect(c.Response().Writer, c.Request(), "/", http.StatusTemporaryRedirect)
			return errors.New(errors.Controller, "apple invalid request", errors.Unauthorized, nil)
		}

		appleUserDetails, err := ac.Service.HandleAppleCallback(callbackRequest.Access)
		if err != nil {
			//http.Redirect(c.Response().Writer, c.Request(), "/", http.StatusTemporaryRedirect)
			return err
		}

		// Add name to the Apple User Detail before call Register.
		appleUserDetails.Name = callbackRequest.Name

		token, err := ac.Service.Register(*appleUserDetails, callbackRequest.Onboarding, callbackRequest.ReferralCode)
		if err != nil {
			//http.Redirect(c.Response().Writer, c.Request(), "/", http.StatusTemporaryRedirect)
			return err
		}

		return c.JSON(http.StatusOK, token)
	}
}

func (ac *controller) HandleAppleLogin() echo.HandlerFunc {
	return func(c echo.Context) error {
		var callbackRequest struct {
			Access string `json:"access"`
		}

		if err := c.Bind(&callbackRequest); err != nil {
			//http.Redirect(c.Response().Writer, c.Request(), "/", http.StatusTemporaryRedirect)
			return errors.New(errors.Controller, "apple invalid request", errors.Unauthorized, nil)
		}

		appleUserDetails, err := ac.Service.HandleAppleCallback(callbackRequest.Access)
		if err != nil {
			//http.Redirect(c.Response().Writer, c.Request(), "/", http.StatusTemporaryRedirect)
			return err
		}

		token, err := ac.Service.Login(*appleUserDetails)
		if err != nil {
			//http.Redirect(c.Response().Writer, c.Request(), "/", http.StatusTemporaryRedirect)
			return err
		}

		return c.JSON(http.StatusOK, token)
	}
}
