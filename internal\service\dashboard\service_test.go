package dashboard

import (
	"context"
	"testing"
	"time"

	"github.com/dsoplabs/dinbora-backend/internal/errors"
	"github.com/dsoplabs/dinbora-backend/internal/model/dashboard"
	"github.com/dsoplabs/dinbora-backend/internal/model/financialsheet"
	"github.com/dsoplabs/dinbora-backend/internal/model/monetary"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/suite"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// Mock repository
type MockDashboardRepository struct {
	mock.Mock
}

func (m *MockDashboardRepository) FindFinancialMap(ctx context.Context, userID string) (*dashboard.FinancialMap, error) {
	args := m.Called(ctx, userID)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*dashboard.FinancialMap), args.Error(1)
}

func (m *MockDashboardRepository) SaveFinancialMap(ctx context.Context, financialMap *dashboard.FinancialMap) error {
	args := m.Called(ctx, financialMap)
	return args.Error(0)
}

func (m *MockDashboardRepository) UpdateFinancialMap(ctx context.Context, financialMap *dashboard.FinancialMap) error {
	args := m.Called(ctx, financialMap)
	return args.Error(0)
}

func (m *MockDashboardRepository) FindIncomeSource(ctx context.Context, id primitive.ObjectID) (*dashboard.IncomeSource, error) {
	args := m.Called(ctx, id)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*dashboard.IncomeSource), args.Error(1)
}

func (m *MockDashboardRepository) FindIncomeSources(ctx context.Context, userID string) ([]*dashboard.IncomeSource, error) {
	args := m.Called(ctx, userID)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).([]*dashboard.IncomeSource), args.Error(1)
}

func (m *MockDashboardRepository) CreateIncomeSource(ctx context.Context, incomeSource *dashboard.IncomeSource) error {
	args := m.Called(ctx, incomeSource)
	return args.Error(0)
}

func (m *MockDashboardRepository) UpdateIncomeSource(ctx context.Context, incomeSource *dashboard.IncomeSource) error {
	args := m.Called(ctx, incomeSource)
	return args.Error(0)
}

func (m *MockDashboardRepository) DeleteIncomeSource(ctx context.Context, id primitive.ObjectID) error {
	args := m.Called(ctx, id)
	return args.Error(0)
}

func (m *MockDashboardRepository) FindStrategicFund(ctx context.Context, userID string) (*dashboard.StrategicFund, error) {
	args := m.Called(ctx, userID)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*dashboard.StrategicFund), args.Error(1)
}

func (m *MockDashboardRepository) CreateStrategicFund(ctx context.Context, strategicFund *dashboard.StrategicFund) error {
	args := m.Called(ctx, strategicFund)
	return args.Error(0)
}

func (m *MockDashboardRepository) UpdateStrategicFund(ctx context.Context, strategicFund *dashboard.StrategicFund) error {
	args := m.Called(ctx, strategicFund)
	return args.Error(0)
}

func (m *MockDashboardRepository) FindInvestment(ctx context.Context, id primitive.ObjectID) (*dashboard.Investment, error) {
	args := m.Called(ctx, id)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*dashboard.Investment), args.Error(1)
}

func (m *MockDashboardRepository) FindInvestments(ctx context.Context, userID string) ([]*dashboard.Investment, error) {
	args := m.Called(ctx, userID)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).([]*dashboard.Investment), args.Error(1)
}

func (m *MockDashboardRepository) CreateInvestment(ctx context.Context, investment *dashboard.Investment) error {
	args := m.Called(ctx, investment)
	return args.Error(0)
}

func (m *MockDashboardRepository) UpdateInvestment(ctx context.Context, investment *dashboard.Investment) error {
	args := m.Called(ctx, investment)
	return args.Error(0)
}

func (m *MockDashboardRepository) DeleteInvestment(ctx context.Context, id primitive.ObjectID) error {
	args := m.Called(ctx, id)
	return args.Error(0)
}

func (m *MockDashboardRepository) FindAsset(ctx context.Context, id primitive.ObjectID) (*dashboard.Asset, error) {
	args := m.Called(ctx, id)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*dashboard.Asset), args.Error(1)
}

func (m *MockDashboardRepository) FindAssets(ctx context.Context, userID string) ([]*dashboard.Asset, error) {
	args := m.Called(ctx, userID)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).([]*dashboard.Asset), args.Error(1)
}

func (m *MockDashboardRepository) CreateAsset(ctx context.Context, asset *dashboard.Asset) error {
	args := m.Called(ctx, asset)
	return args.Error(0)
}

func (m *MockDashboardRepository) UpdateAsset(ctx context.Context, asset *dashboard.Asset) error {
	args := m.Called(ctx, asset)
	return args.Error(0)
}

func (m *MockDashboardRepository) DeleteAsset(ctx context.Context, id primitive.ObjectID) error {
	args := m.Called(ctx, id)
	return args.Error(0)
}

func (m *MockDashboardRepository) FindNetWorthHistory(ctx context.Context, userID string, limit int) ([]*dashboard.NetWorthSnapshot, error) {
	args := m.Called(ctx, userID, limit)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).([]*dashboard.NetWorthSnapshot), args.Error(1)
}

func (m *MockDashboardRepository) SaveNetWorthSnapshot(ctx context.Context, snapshot *dashboard.NetWorthSnapshot) error {
	args := m.Called(ctx, snapshot)
	return args.Error(0)
}

func (m *MockDashboardRepository) FindLatestNetWorthSnapshot(ctx context.Context) (time.Time, error) {
	args := m.Called(ctx)
	return args.Get(0).(time.Time), args.Error(1)
}

func (m *MockDashboardRepository) FindFinancialIndependence(ctx context.Context, userID string) (*dashboard.FinancialIndependence, error) {
	args := m.Called(ctx, userID)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*dashboard.FinancialIndependence), args.Error(1)
}

func (m *MockDashboardRepository) FindFinancialIndependenceByUser(ctx context.Context, userID string) (*dashboard.FinancialIndependence, error) {
	args := m.Called(ctx, userID)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*dashboard.FinancialIndependence), args.Error(1)
}

func (m *MockDashboardRepository) CreateFinancialIndependence(ctx context.Context, fi *dashboard.FinancialIndependence) error {
	args := m.Called(ctx, fi)
	return args.Error(0)
}

func (m *MockDashboardRepository) UpdateFinancialIndependence(ctx context.Context, fi *dashboard.FinancialIndependence) error {
	args := m.Called(ctx, fi)
	return args.Error(0)
}

func (m *MockDashboardRepository) DeleteFinancialIndependence(ctx context.Context, id primitive.ObjectID) error {
	args := m.Called(ctx, id)
	return args.Error(0)
}

// Mock financial sheet service
type MockFinancialSheetService struct {
	mock.Mock
}

func (m *MockFinancialSheetService) FindAllTransactions(ctx context.Context, userID string, categoryType financialsheet.CategoryType, year int, month int) ([]*financialsheet.Transaction, error) {
	args := m.Called(ctx, userID, categoryType, year, month)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).([]*financialsheet.Transaction), args.Error(1)
}

func (m *MockFinancialSheetService) CountUserCategories(ctx context.Context, userID string) (int, error) {
	args := m.Called(ctx, userID)
	return args.Int(0), args.Error(1)
}

// Stub implementations for methods not used in dashboard service but required by interface
func (m *MockFinancialSheetService) Find(ctx context.Context, id string) (*financialsheet.Record, error) {
	return nil, nil
}

func (m *MockFinancialSheetService) FindByUser(ctx context.Context, userID string) (*financialsheet.Record, error) {
	return nil, nil
}

func (m *MockFinancialSheetService) FindByUserAndPeriod(ctx context.Context, userID string, year, month int, flatten bool) (*financialsheet.Record, error) {
	return nil, nil
}

func (m *MockFinancialSheetService) Update(ctx context.Context, record *financialsheet.Record) error {
	return nil
}

func (m *MockFinancialSheetService) Delete(ctx context.Context, id string) error {
	return nil
}

func (m *MockFinancialSheetService) CreateCategory(ctx context.Context, category *financialsheet.Category, userID string) (*financialsheet.Category, error) {
	return nil, nil
}

func (m *MockFinancialSheetService) FindCategory(ctx context.Context, id string) (*financialsheet.Category, error) {
	return nil, nil
}

func (m *MockFinancialSheetService) FindAllCategories(ctx context.Context) ([]*financialsheet.Category, error) {
	return nil, nil
}

func (m *MockFinancialSheetService) FindAllCategoriesByUser(ctx context.Context, userID string) ([]*financialsheet.Category, error) {
	return nil, nil
}

func (m *MockFinancialSheetService) FindCategoryByIdentifier(ctx context.Context, identifier financialsheet.CategoryIdentifier) (*financialsheet.Category, error) {
	return nil, nil
}

func (m *MockFinancialSheetService) UpdateCategory(ctx context.Context, category *financialsheet.Category) error {
	return nil
}

func (m *MockFinancialSheetService) DeleteCategory(ctx context.Context, id string, userID string) error {
	return nil
}

func (m *MockFinancialSheetService) CreateTransaction(ctx context.Context, record *financialsheet.Record, transaction *financialsheet.Transaction) (*financialsheet.Record, error) {
	return nil, nil
}

func (m *MockFinancialSheetService) CreateDreamTransaction(ctx context.Context, userID string, dreamID string, amount monetary.Amount) error {
	return nil
}

func (m *MockFinancialSheetService) FindTransaction(ctx context.Context, recordID string, transactionID string) (*financialsheet.Transaction, error) {
	return nil, nil
}

func (m *MockFinancialSheetService) UpdateTransaction(ctx context.Context, record *financialsheet.Record, transaction *financialsheet.Transaction) (*financialsheet.Record, error) {
	return nil, nil
}

func (m *MockFinancialSheetService) DeleteTransaction(ctx context.Context, record *financialsheet.Record, transactionID string) (*financialsheet.Record, error) {
	return nil, nil
}

func (m *MockFinancialSheetService) Initialize(ctx context.Context, userID string, userName string) error {
	return nil
}

// Test suite
type DashboardServiceTestSuite struct {
	suite.Suite
	service                   Service
	mockRepo                  *MockDashboardRepository
	mockFinancialSheetService *MockFinancialSheetService
	testUserID                string
}

func (suite *DashboardServiceTestSuite) SetupTest() {
	suite.mockRepo = new(MockDashboardRepository)
	suite.mockFinancialSheetService = new(MockFinancialSheetService)
	suite.service = New(suite.mockRepo, suite.mockFinancialSheetService)
	suite.testUserID = "test-user-123"
}

func TestDashboardServiceTestSuite(t *testing.T) {
	suite.Run(t, new(DashboardServiceTestSuite))
}

func (suite *DashboardServiceTestSuite) TestCreateIncomeSource() {
	ctx := context.Background()

	// Mock FindFinancialMap to return an existing financial map
	existingFinancialMap := &dashboard.FinancialMap{
		UserID:          suite.testUserID,
		MonthlyIncome:   0,
		IncomeSources:   []*dashboard.IncomeSource{},
		Investments:     []*dashboard.Investment{},
		Assets:          []*dashboard.Asset{},
		NetWorthHistory: []*dashboard.NetWorthSnapshot{},
	}
	suite.mockRepo.On("FindFinancialMap", ctx, suite.testUserID).Return(existingFinancialMap, nil)
	suite.mockRepo.On("UpdateFinancialMap", ctx, mock.AnythingOfType("*dashboard.FinancialMap")).Return(nil)

	incomeSource, err := suite.service.CreateIncomeSource(ctx, suite.testUserID, "Salary", 5000)

	suite.Require().NoError(err)
	suite.Equal("Salary", incomeSource.Name)
	suite.Equal(monetary.Amount(5000), incomeSource.MonthlyAmount)
	suite.Equal(suite.testUserID, incomeSource.UserID)
	suite.False(incomeSource.Blocked, "User-created income source should not be blocked")
	suite.mockRepo.AssertExpectations(suite.T())
}

func (suite *DashboardServiceTestSuite) TestCreateIncomeSource_ValidationError() {
	ctx := context.Background()

	// Mock FindFinancialMap to return an existing financial map
	existingFinancialMap := &dashboard.FinancialMap{
		UserID:          suite.testUserID,
		MonthlyIncome:   0,
		IncomeSources:   []*dashboard.IncomeSource{},
		Investments:     []*dashboard.Investment{},
		Assets:          []*dashboard.Asset{},
		NetWorthHistory: []*dashboard.NetWorthSnapshot{},
	}
	suite.mockRepo.On("FindFinancialMap", ctx, suite.testUserID).Return(existingFinancialMap, nil)

	// Test with empty name
	_, err := suite.service.CreateIncomeSource(ctx, suite.testUserID, "", 5000)

	suite.Error(err)
	suite.Contains(err.Error(), "income source name is required")
}

func (suite *DashboardServiceTestSuite) TestUpdateStrategicFund_CreateNew() {
	ctx := context.Background()

	// Mock FindFinancialMap to return a financial map without emergency fund
	existingFinancialMap := &dashboard.FinancialMap{
		UserID:          suite.testUserID,
		MonthlyIncome:   0,
		StrategicFund:   nil, // No emergency fund exists
		IncomeSources:   []*dashboard.IncomeSource{},
		Investments:     []*dashboard.Investment{},
		Assets:          []*dashboard.Asset{},
		NetWorthHistory: []*dashboard.NetWorthSnapshot{},
	}
	suite.mockRepo.On("FindFinancialMap", ctx, suite.testUserID).Return(existingFinancialMap, nil)
	suite.mockRepo.On("UpdateFinancialMap", ctx, mock.AnythingOfType("*dashboard.FinancialMap")).Return(nil)

	err := suite.service.UpdateStrategicFund(ctx, suite.testUserID, 10000)

	suite.Require().NoError(err)
	suite.mockRepo.AssertExpectations(suite.T())
}

func (suite *DashboardServiceTestSuite) TestUpdateStrategicFund_UpdateExisting() {
	ctx := context.Background()

	existingFund := &dashboard.StrategicFund{
		ObjectID:     primitive.NewObjectID(),
		UserID:       suite.testUserID,
		CurrentValue: 5000,
		GoalValue:    50000,
	}

	// Mock FindFinancialMap to return a financial map with existing emergency fund
	existingFinancialMap := &dashboard.FinancialMap{
		UserID:          suite.testUserID,
		MonthlyIncome:   0,
		StrategicFund:   existingFund,
		IncomeSources:   []*dashboard.IncomeSource{},
		Investments:     []*dashboard.Investment{},
		Assets:          []*dashboard.Asset{},
		NetWorthHistory: []*dashboard.NetWorthSnapshot{},
	}
	suite.mockRepo.On("FindFinancialMap", ctx, suite.testUserID).Return(existingFinancialMap, nil)
	suite.mockRepo.On("UpdateFinancialMap", ctx, mock.AnythingOfType("*dashboard.FinancialMap")).Return(nil)

	err := suite.service.UpdateStrategicFund(ctx, suite.testUserID, 15000)

	suite.Require().NoError(err)
	suite.mockRepo.AssertExpectations(suite.T())
}

func (suite *DashboardServiceTestSuite) TestCreateMonthlySnapshot() {
	ctx := context.Background()

	// Mock data
	strategicFund := &dashboard.StrategicFund{
		UserID:       suite.testUserID,
		CurrentValue: 10000,
		GoalValue:    50000,
	}

	investments := []*dashboard.Investment{
		{UserID: suite.testUserID, Name: "Investment 1", CurrentValue: 15000},
		{UserID: suite.testUserID, Name: "Investment 2", CurrentValue: 10000},
	}

	assets := []*dashboard.Asset{
		{UserID: suite.testUserID, Name: "Car", CurrentValue: 80000},
	}

	// Mock FindFinancialMap to return a complete financial map
	existingFinancialMap := &dashboard.FinancialMap{
		UserID:           suite.testUserID,
		MonthlyIncome:    0,
		StrategicFund:    strategicFund,
		TotalInvestments: 25000,
		TotalAssets:      80000,
		IncomeSources:    []*dashboard.IncomeSource{},
		Investments:      investments,
		Assets:           assets,
		NetWorthHistory:  []*dashboard.NetWorthSnapshot{},
	}
	suite.mockRepo.On("FindFinancialMap", ctx, suite.testUserID).Return(existingFinancialMap, nil)
	suite.mockRepo.On("UpdateFinancialMap", ctx, mock.AnythingOfType("*dashboard.FinancialMap")).Return(nil)

	err := suite.service.CreateMonthlySnapshot(ctx, suite.testUserID, time.Now(), time.Now())

	suite.Require().NoError(err)
	suite.mockRepo.AssertExpectations(suite.T())
}

func (suite *DashboardServiceTestSuite) TestFindIncomeSources_WithLiveData() {
	ctx := context.Background()

	// Mock existing financial map with stale income sources
	existingFinancialMap := &dashboard.FinancialMap{
		UserID:        suite.testUserID,
		MonthlyIncome: 3000, // Old total
		IncomeSources: []*dashboard.IncomeSource{
			{
				UserID:        suite.testUserID,
				Name:          "Old Income",
				MonthlyAmount: 3000,
				MoneySource:   financialsheet.MoneySourceOpt1,
				Blocked:       false, // This is a user-created income source (not blocked)
			},
		},
		Investments:     []*dashboard.Investment{},
		Assets:          []*dashboard.Asset{},
		NetWorthHistory: []*dashboard.NetWorthSnapshot{},
	}

	// Mock live transactions that should update the income sources
	liveTransactions := []*financialsheet.Transaction{
		{
			Value:       10000,
			MoneySource: financialsheet.MoneySourceOpt2,
			Category:    financialsheet.CategoryIdentifierBenefits,
			Type:        financialsheet.CategoryTypeIncome,
		},
		{
			Value:       50000,
			MoneySource: financialsheet.MoneySourceOpt1,
			Category:    financialsheet.CategoryIdentifierCompensation,
			Type:        financialsheet.CategoryTypeIncome,
		},
	}

	// Setup mocks
	suite.mockRepo.On("FindFinancialMap", ctx, suite.testUserID).Return(existingFinancialMap, nil)
	suite.mockFinancialSheetService.On("FindAllTransactions", ctx, suite.testUserID, financialsheet.CategoryTypeIncome, mock.AnythingOfType("int"), mock.AnythingOfType("int")).Return(liveTransactions, nil)
	// Mock the UpdateFinancialMap call that will be made when income sources change
	suite.mockRepo.On("UpdateFinancialMap", ctx, mock.AnythingOfType("*dashboard.FinancialMap")).Return(nil)

	// Call FindIncomeSources
	incomeSources, err := suite.service.FindIncomeSources(ctx, suite.testUserID)

	// Verify results
	suite.Require().NoError(err)
	suite.Require().Len(incomeSources, 3) // Should have 3 income sources: user-created + 2 from live data

	// Verify the income sources are updated with live data
	incomeSourcesByName := make(map[string]*dashboard.IncomeSource)
	for _, source := range incomeSources {
		incomeSourcesByName[source.Name] = source
	}

	// Check that we have the user-created income source preserved
	suite.Contains(incomeSourcesByName, "Old Income")
	suite.False(incomeSourcesByName["Old Income"].Blocked, "User-created income source should not be blocked")

	// Check that we have the expected income sources from live transactions
	suite.Contains(incomeSourcesByName, "Bolsa Família")
	suite.True(incomeSourcesByName["Bolsa Família"].Blocked, "Live income source should be blocked")
	suite.Contains(incomeSourcesByName, "Salário CLT")
	suite.True(incomeSourcesByName["Salário CLT"].Blocked, "Live income source should be blocked")

	suite.mockRepo.AssertExpectations(suite.T())
	suite.mockFinancialSheetService.AssertExpectations(suite.T())
}

func (suite *DashboardServiceTestSuite) TestFindFinancialMap_WithLiveDataPersistence() {
	ctx := context.Background()

	// Mock existing financial map with stale income sources
	existingFinancialMap := &dashboard.FinancialMap{
		UserID:        suite.testUserID,
		MonthlyIncome: 2000, // Old total
		StrategicFund: &dashboard.StrategicFund{
			ObjectID:     primitive.NewObjectID(),
			UserID:       suite.testUserID,
			CurrentValue: 1000,
			GoalValue:    4200000,
		},
		IncomeSources: []*dashboard.IncomeSource{
			{
				UserID:        suite.testUserID,
				Name:          "Stale Income",
				MonthlyAmount: 2000,
				MoneySource:   financialsheet.MoneySourceOpt3,
				Blocked:       false, // This is a user-created income source (not blocked)
			},
		},
		Investments:     []*dashboard.Investment{},
		Assets:          []*dashboard.Asset{},
		NetWorthHistory: []*dashboard.NetWorthSnapshot{},
	}

	// Mock live transactions that should update the income sources
	liveTransactions := []*financialsheet.Transaction{
		{
			Value:       15000,
			MoneySource: financialsheet.MoneySourceOpt1,
			Category:    financialsheet.CategoryIdentifierCompensation,
			Type:        financialsheet.CategoryTypeIncome,
		},
	}

	// Setup mocks
	suite.mockRepo.On("FindFinancialMap", ctx, suite.testUserID).Return(existingFinancialMap, nil)
	suite.mockFinancialSheetService.On("FindAllTransactions", ctx, suite.testUserID, financialsheet.CategoryTypeIncome, mock.AnythingOfType("int"), mock.AnythingOfType("int")).Return(liveTransactions, nil)

	// Mock strategic fund queries - return NotFound, then no transactions
	notFoundErr := errors.New(errors.Repository, "strategic fund not found", errors.NotFound, nil)
	suite.mockRepo.On("FindStrategicFund", ctx, suite.testUserID).Return(nil, notFoundErr)
	suite.mockFinancialSheetService.On("FindAllTransactions", ctx, suite.testUserID, financialsheet.CategoryTypeCostsOfLiving, 0, 0).Return([]*financialsheet.Transaction{}, nil)

	// Mock the UpdateFinancialMap call that will be made when income sources change
	suite.mockRepo.On("UpdateFinancialMap", ctx, mock.AnythingOfType("*dashboard.FinancialMap")).Return(nil)

	// Call FindFinancialMap
	financialMap, err := suite.service.FindFinancialMap(ctx, suite.testUserID)

	// Verify results
	suite.Require().NoError(err)
	suite.Require().NotNil(financialMap)
	suite.Require().Len(financialMap.IncomeSources, 2) // Should have 2 income sources: user-created + live data

	// Create a map for easier verification
	incomeSourcesByName := make(map[string]*dashboard.IncomeSource)
	for _, source := range financialMap.IncomeSources {
		incomeSourcesByName[source.Name] = source
	}

	// Verify the user-created income source is preserved
	staleIncome, exists := incomeSourcesByName["Stale Income"]
	suite.True(exists, "User-created income source should be preserved")
	suite.Equal("Stale Income", staleIncome.Name)
	suite.Equal(monetary.Amount(2000), staleIncome.MonthlyAmount)
	suite.Equal(financialsheet.MoneySourceOpt3, staleIncome.MoneySource)
	suite.False(staleIncome.Blocked, "User-created income source should not be blocked")

	// Verify the live income source is added
	salarioCLT, exists := incomeSourcesByName["Salário CLT"]
	suite.True(exists, "Live income source should be added")
	suite.Equal("Salário CLT", salarioCLT.Name)
	suite.Equal(monetary.Amount(15000), salarioCLT.MonthlyAmount)
	suite.Equal(financialsheet.MoneySourceOpt1, salarioCLT.MoneySource)
	suite.True(salarioCLT.Blocked, "Live income source should be blocked")

	// Verify total monthly income (2000 + 15000 = 17000)
	suite.Equal(monetary.Amount(17000), financialMap.MonthlyIncome)

	// Verify that UpdateFinancialMap was called to persist the changes
	suite.mockRepo.AssertExpectations(suite.T())
	suite.mockFinancialSheetService.AssertExpectations(suite.T())
}

func (suite *DashboardServiceTestSuite) TestFindFinancialMap_NoChangesNoPersistence() {
	ctx := context.Background()

	// Mock existing financial map with current income sources
	existingFinancialMap := &dashboard.FinancialMap{
		UserID:        suite.testUserID,
		MonthlyIncome: 15000,
		StrategicFund: &dashboard.StrategicFund{
			ObjectID:     primitive.NewObjectID(),
			UserID:       suite.testUserID,
			CurrentValue: 0,
			GoalValue:    4200000,
		},
		IncomeSources: []*dashboard.IncomeSource{
			{
				UserID:        suite.testUserID,
				Name:          "Salário CLT",
				MonthlyAmount: 15000,
				MoneySource:   financialsheet.MoneySourceOpt1,
				Icon:          financialsheet.CategoryIconCompensationFormal,
				Blocked:       true, // This income source is from financial sheet (blocked)
			},
		},
		Investments:     []*dashboard.Investment{},
		Assets:          []*dashboard.Asset{},
		NetWorthHistory: []*dashboard.NetWorthSnapshot{},
	}

	// Mock live transactions that match existing income sources (no changes)
	liveTransactions := []*financialsheet.Transaction{
		{
			Value:       15000,
			MoneySource: financialsheet.MoneySourceOpt1,
			Category:    financialsheet.CategoryIdentifierCompensation,
			Type:        financialsheet.CategoryTypeIncome,
		},
	}

	// Setup mocks - Note: NO UpdateFinancialMap mock since it shouldn't be called
	suite.mockRepo.On("FindFinancialMap", ctx, suite.testUserID).Return(existingFinancialMap, nil)
	suite.mockFinancialSheetService.On("FindAllTransactions", ctx, suite.testUserID, financialsheet.CategoryTypeIncome, mock.AnythingOfType("int"), mock.AnythingOfType("int")).Return(liveTransactions, nil)

	// Mock strategic fund queries - return NotFound, then no transactions
	notFoundErr := errors.New(errors.Repository, "strategic fund not found", errors.NotFound, nil)
	suite.mockRepo.On("FindStrategicFund", ctx, suite.testUserID).Return(nil, notFoundErr)
	suite.mockFinancialSheetService.On("FindAllTransactions", ctx, suite.testUserID, financialsheet.CategoryTypeCostsOfLiving, 0, 0).Return([]*financialsheet.Transaction{}, nil)

	// Call FindFinancialMap
	financialMap, err := suite.service.FindFinancialMap(ctx, suite.testUserID)

	// Verify results
	suite.Require().NoError(err)
	suite.Require().NotNil(financialMap)
	suite.Require().Len(financialMap.IncomeSources, 1)

	// Verify the income source remains the same
	suite.Equal("Salário CLT", financialMap.IncomeSources[0].Name)
	suite.Equal(monetary.Amount(15000), financialMap.IncomeSources[0].MonthlyAmount)

	// Verify that UpdateFinancialMap was NOT called since no changes occurred
	suite.mockRepo.AssertExpectations(suite.T())
	suite.mockFinancialSheetService.AssertExpectations(suite.T())
}

func (suite *DashboardServiceTestSuite) TestHasIncomeSourcesChanged() {
	// Test case 1: Different number of income sources
	original1 := &dashboard.FinancialMap{
		IncomeSources: []*dashboard.IncomeSource{
			{Name: "Source1", MonthlyAmount: 1000},
		},
	}
	updated1 := &dashboard.FinancialMap{
		IncomeSources: []*dashboard.IncomeSource{
			{Name: "Source1", MonthlyAmount: 1000},
			{Name: "Source2", MonthlyAmount: 2000},
		},
	}
	suite.True(suite.service.(*service).hasIncomeSourcesChanged(original1, updated1))

	// Test case 2: Same income sources, no changes
	original2 := &dashboard.FinancialMap{
		IncomeSources: []*dashboard.IncomeSource{
			{Name: "Source1", MonthlyAmount: 1000, MoneySource: financialsheet.MoneySourceOpt1, Icon: "icon1"},
		},
	}
	updated2 := &dashboard.FinancialMap{
		IncomeSources: []*dashboard.IncomeSource{
			{Name: "Source1", MonthlyAmount: 1000, MoneySource: financialsheet.MoneySourceOpt1, Icon: "icon1"},
		},
	}
	suite.False(suite.service.(*service).hasIncomeSourcesChanged(original2, updated2))

	// Test case 3: Same name but different amount
	original3 := &dashboard.FinancialMap{
		IncomeSources: []*dashboard.IncomeSource{
			{Name: "Source1", MonthlyAmount: 1000, MoneySource: financialsheet.MoneySourceOpt1, Icon: "icon1"},
		},
	}
	updated3 := &dashboard.FinancialMap{
		IncomeSources: []*dashboard.IncomeSource{
			{Name: "Source1", MonthlyAmount: 2000, MoneySource: financialsheet.MoneySourceOpt1, Icon: "icon1"},
		},
	}
	suite.True(suite.service.(*service).hasIncomeSourcesChanged(original3, updated3))

	// Test case 4: Different names (completely different sources)
	original4 := &dashboard.FinancialMap{
		IncomeSources: []*dashboard.IncomeSource{
			{Name: "Old Income", MonthlyAmount: 3000, MoneySource: financialsheet.MoneySourceOpt1},
		},
	}
	updated4 := &dashboard.FinancialMap{
		IncomeSources: []*dashboard.IncomeSource{
			{Name: "Bolsa Família", MonthlyAmount: 10000, MoneySource: financialsheet.MoneySourceOpt2},
			{Name: "Salário CLT", MonthlyAmount: 50000, MoneySource: financialsheet.MoneySourceOpt1},
		},
	}
	suite.True(suite.service.(*service).hasIncomeSourcesChanged(original4, updated4))
}

func (suite *DashboardServiceTestSuite) TestAggregateIncomeSourcesFromTransactions() {
	ctx := context.Background()

	// Create mock transactions with different money sources for the same category
	transactions := []*financialsheet.Transaction{
		{
			Category:    financialsheet.CategoryIdentifierBenefits,
			MoneySource: financialsheet.MoneySourceOpt2, // Should be "Bolsa Família"
			Value:       10000,
			Type:        financialsheet.CategoryTypeIncome,
		},
		{
			Category:    financialsheet.CategoryIdentifierBenefits,
			MoneySource: financialsheet.MoneySourceOpt2, // Should be "Bolsa Família"
			Value:       10000,
			Type:        financialsheet.CategoryTypeIncome,
		},
		{
			Category:    financialsheet.CategoryIdentifierBenefits,
			MoneySource: financialsheet.MoneySourceOpt2, // Should be "Bolsa Família"
			Value:       10000,
			Type:        financialsheet.CategoryTypeIncome,
		},
		{
			Category:    financialsheet.CategoryIdentifierCompensation,
			MoneySource: financialsheet.MoneySourceOpt1, // Should be "Salário CLT"
			Value:       50000,
			Type:        financialsheet.CategoryTypeIncome,
		},
	}

	// Mock the financial sheet service to return these transactions
	suite.mockFinancialSheetService.On("FindAllTransactions", ctx, suite.testUserID, financialsheet.CategoryTypeIncome, mock.AnythingOfType("int"), mock.AnythingOfType("int")).Return(transactions, nil)

	// Mock strategic fund queries - first call should return NotFound, then no transactions
	notFoundErr := errors.New(errors.Repository, "strategic fund not found", errors.NotFound, nil)
	suite.mockRepo.On("FindStrategicFund", ctx, suite.testUserID).Return(nil, notFoundErr)
	suite.mockFinancialSheetService.On("FindAllTransactions", ctx, suite.testUserID, financialsheet.CategoryTypeCostsOfLiving, 0, 0).Return([]*financialsheet.Transaction{}, nil)

	// Mock repository to return NotFound (no existing financial map)
	notFoundFinancialMapErr := errors.New(errors.Repository, "financial map not found", errors.NotFound, nil)
	suite.mockRepo.On("FindFinancialMap", ctx, suite.testUserID).Return(nil, notFoundFinancialMapErr)
	suite.mockRepo.On("SaveFinancialMap", ctx, mock.AnythingOfType("*dashboard.FinancialMap")).Return(nil)

	// Call the method
	financialMap, err := suite.service.FindFinancialMap(ctx, suite.testUserID)

	// Verify results
	suite.Require().NoError(err)
	suite.Require().NotNil(financialMap)
	suite.Require().Len(financialMap.IncomeSources, 2) // Should have 2 distinct income sources

	// Verify the income sources are correctly aggregated by money source name
	incomeSourcesByName := make(map[string]*dashboard.IncomeSource)
	for _, source := range financialMap.IncomeSources {
		incomeSourcesByName[source.Name] = source
	}

	// Check "Bolsa Família" aggregation (3 transactions of 10000 each = 30000 total)
	bolsaFamilia, exists := incomeSourcesByName["Bolsa Família"]
	suite.True(exists, "Should have 'Bolsa Família' income source")
	suite.Equal("Bolsa Família", bolsaFamilia.Name)
	suite.Equal(monetary.Amount(30000), bolsaFamilia.MonthlyAmount)
	suite.Equal(financialsheet.MoneySourceOpt2, bolsaFamilia.MoneySource)
	suite.True(bolsaFamilia.Blocked, "Income source from financial sheet should be blocked")

	// Check "Salário CLT" (1 transaction of 50000)
	salarioCLT, exists := incomeSourcesByName["Salário CLT"]
	suite.True(exists, "Should have 'Salário CLT' income source")
	suite.Equal("Salário CLT", salarioCLT.Name)
	suite.Equal(monetary.Amount(50000), salarioCLT.MonthlyAmount)
	suite.Equal(financialsheet.MoneySourceOpt1, salarioCLT.MoneySource)
	suite.True(salarioCLT.Blocked, "Income source from financial sheet should be blocked")

	// Verify total monthly income
	suite.Equal(monetary.Amount(80000), financialMap.MonthlyIncome)

	suite.mockRepo.AssertExpectations(suite.T())
	suite.mockFinancialSheetService.AssertExpectations(suite.T())
}

func (suite *DashboardServiceTestSuite) TestUpdateIncomeSource_BlockedSource() {
	ctx := context.Background()
	incomeSourceID := primitive.NewObjectID()

	// Create a blocked income source (from financial sheet)
	blockedIncomeSource := &dashboard.IncomeSource{
		ObjectID:      incomeSourceID,
		UserID:        suite.testUserID,
		Name:          "Salary",
		MonthlyAmount: 5000,
		Blocked:       true, // This income source is blocked (read-only)
	}

	// Mock repository to return the blocked income source
	suite.mockRepo.On("FindIncomeSource", ctx, incomeSourceID).Return(blockedIncomeSource, nil)

	// Mock FindFinancialMap to return a financial map containing the blocked income source
	financialMap := &dashboard.FinancialMap{
		UserID:        suite.testUserID,
		IncomeSources: []*dashboard.IncomeSource{blockedIncomeSource},
	}
	suite.mockRepo.On("FindFinancialMap", ctx, suite.testUserID).Return(financialMap, nil)

	// Try to update the blocked income source
	err := suite.service.UpdateIncomeSource(ctx, incomeSourceID.Hex(), "Updated Salary", 6000)

	// Should return a Forbidden error
	suite.Error(err)
	suite.Contains(err.Error(), "cannot update blocked income source")
	suite.mockRepo.AssertExpectations(suite.T())
}

func (suite *DashboardServiceTestSuite) TestDeleteIncomeSource_BlockedSource() {
	ctx := context.Background()
	incomeSourceID := primitive.NewObjectID()

	// Create a blocked income source (from financial sheet)
	blockedIncomeSource := &dashboard.IncomeSource{
		ObjectID:      incomeSourceID,
		UserID:        suite.testUserID,
		Name:          "Salary",
		MonthlyAmount: 5000,
		Blocked:       true, // This income source is blocked (read-only)
	}

	// Mock repository to return the blocked income source
	suite.mockRepo.On("FindIncomeSource", ctx, incomeSourceID).Return(blockedIncomeSource, nil)

	// Mock FindFinancialMap to return a financial map containing the blocked income source
	financialMap := &dashboard.FinancialMap{
		UserID:        suite.testUserID,
		IncomeSources: []*dashboard.IncomeSource{blockedIncomeSource},
	}
	suite.mockRepo.On("FindFinancialMap", ctx, suite.testUserID).Return(financialMap, nil)

	// Try to delete the blocked income source
	err := suite.service.DeleteIncomeSource(ctx, incomeSourceID.Hex())

	// Should return a Forbidden error
	suite.Error(err)
	suite.Contains(err.Error(), "cannot delete blocked income source")
	suite.mockRepo.AssertExpectations(suite.T())
}

func TestIsIncomeSourceModifiable(t *testing.T) {
	// Test with non-blocked income source (user-created)
	userCreatedSource := &dashboard.IncomeSource{
		Name:    "User Salary",
		Blocked: false,
	}
	assert.True(t, isIncomeSourceModifiable(userCreatedSource), "User-created income source should be modifiable")

	// Test with blocked income source (from financial sheet)
	blockedSource := &dashboard.IncomeSource{
		Name:    "Financial Sheet Salary",
		Blocked: true,
	}
	assert.False(t, isIncomeSourceModifiable(blockedSource), "Blocked income source should not be modifiable")
}

func TestIsNotFoundError(t *testing.T) {
	// Test with NotFound error
	notFoundErr := errors.New(errors.Repository, "not found", errors.NotFound, nil)
	assert.True(t, isNotFoundError(notFoundErr))

	// Test with other error
	internalErr := errors.New(errors.Repository, "internal error", errors.Internal, nil)
	assert.False(t, isNotFoundError(internalErr))

	// Test with non-domain error
	regularErr := assert.AnError
	assert.False(t, isNotFoundError(regularErr))
}

func TestCalculateTopMoneySourceVariations(t *testing.T) {
	// Create service instance
	mockRepo := &MockDashboardRepository{}
	mockFinancialSheetService := &MockFinancialSheetService{}
	service := &service{
		Repository:            mockRepo,
		FinancialSheetService: mockFinancialSheetService,
	}

	// Create test transactions for current period
	currentTransactions := []*financialsheet.Transaction{
		{
			MoneySource: financialsheet.MoneySourceOpt1, // Housing rent/mortgage
			Category:    financialsheet.CategoryIdentifierHousing,
			Type:        financialsheet.CategoryTypeExpense,
			Value:       1500, // $1500
		},
		{
			MoneySource: financialsheet.MoneySourceOpt4, // Housing insurance
			Category:    financialsheet.CategoryIdentifierHousing,
			Type:        financialsheet.CategoryTypeExpense,
			Value:       300, // $300
		},
		{
			MoneySource: financialsheet.MoneySourceOpt3, // Transportation fuel
			Category:    financialsheet.CategoryIdentifierTransportation,
			Type:        financialsheet.CategoryTypeExpense,
			Value:       200, // $200
		},
	}

	// Create test transactions for previous period
	previousTransactions := []*financialsheet.Transaction{
		{
			MoneySource: financialsheet.MoneySourceOpt1, // Housing rent/mortgage
			Category:    financialsheet.CategoryIdentifierHousing,
			Type:        financialsheet.CategoryTypeExpense,
			Value:       1400, // $1400 (increased by $100)
		},
		{
			MoneySource: financialsheet.MoneySourceOpt4, // Housing insurance
			Category:    financialsheet.CategoryIdentifierHousing,
			Type:        financialsheet.CategoryTypeExpense,
			Value:       350, // $350 (decreased by $50)
		},
		{
			MoneySource: financialsheet.MoneySourceOpt3, // Transportation fuel
			Category:    financialsheet.CategoryIdentifierTransportation,
			Type:        financialsheet.CategoryTypeExpense,
			Value:       200, // $200 (no change)
		},
	}

	// Test increases
	increases := service.calculateTopMoneySourceVariations(currentTransactions, previousTransactions, true)
	assert.Len(t, increases, 1, "Should have 1 increase")
	assert.Equal(t, "housing_rent_mortgage", increases[0].MoneySourceIdentifier)
	assert.Equal(t, monetary.Amount(100), increases[0].Amount)
	assert.Equal(t, "increase", increases[0].Direction)
	assert.InDelta(t, 7.14, increases[0].Percentage, 0.01) // 100/1400 * 100 ≈ 7.14%

	// Test reductions
	reductions := service.calculateTopMoneySourceVariations(currentTransactions, previousTransactions, false)
	assert.Len(t, reductions, 1, "Should have 1 reduction")
	assert.Equal(t, "housing_insurance", reductions[0].MoneySourceIdentifier)
	assert.Equal(t, monetary.Amount(50), reductions[0].Amount)
	assert.Equal(t, "decrease", reductions[0].Direction)
	assert.InDelta(t, -14.29, reductions[0].Percentage, 0.01) // -50/350 * 100 ≈ -14.29%
}
