package billing

import (
	"context"
	"time"

	"github.com/dsoplabs/dinbora-backend/internal/errors"
	"github.com/dsoplabs/dinbora-backend/internal/model/billing"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// Subscription CRUD operations
func (s *service) CreateSubscription(ctx context.Context, userID, planID primitive.ObjectID, provider billing.PaymentProvider) (*billing.Subscription, error) {
	// Validate the plan
	plan, err := s.ValidatePlanForSubscription(ctx, planID)
	if err != nil {
		return nil, err
	}

	// Create subscription with plan defaults
	now := time.Now()
	subscription := &billing.Subscription{
		UserID:    userID,
		PlanID:    planID,
		Provider:  provider,
		Status:    billing.SubscriptionStatusTrial, // Start with trial
		StartDate: now,
		EndDate:   billing.SubscriptionHelperInstance.CalculateEndDate(now, plan.DurationMonths),
		AutoRenew: plan.AutoRenew,
	}

	// Set trial end date if plan has trial period
	if plan.TrialDays > 0 {
		subscription.TrialEndDate = billing.SubscriptionHelperInstance.CalculateTrialEndDate(now, plan.TrialDays)
	} else {
		// No trial, start as active
		subscription.Status = billing.SubscriptionStatusActive
	}

	subscription.SetDefaults()

	// Create the subscription
	subscriptionID, err := s.repo.Subscriptions().Create(ctx, subscription)
	if err != nil {
		return nil, err
	}

	// Convert string ID back to ObjectID for return
	objectID, err := primitive.ObjectIDFromHex(subscriptionID)
	if err != nil {
		return nil, errors.New(errors.Service, "failed to convert subscription ID", errors.Internal, err)
	}

	subscription.ObjectID = objectID
	subscription.ID = subscriptionID

	return subscription, nil
}

func (s *service) FindSubscription(ctx context.Context, id primitive.ObjectID) (*billing.Subscription, error) {
	return s.repo.Subscriptions().Find(ctx, id)
}

func (s *service) FindUserSubscriptions(ctx context.Context, userID primitive.ObjectID) ([]*billing.Subscription, error) {
	return s.repo.Subscriptions().FindByUser(ctx, userID)
}

func (s *service) FindActiveUserSubscriptions(ctx context.Context, userID primitive.ObjectID) ([]*billing.Subscription, error) {
	return s.repo.Subscriptions().FindActiveByUser(ctx, userID)
}

func (s *service) UpdateSubscription(ctx context.Context, subscription *billing.Subscription) error {
	return s.repo.Subscriptions().Update(ctx, subscription)
}

func (s *service) DeleteSubscription(ctx context.Context, id primitive.ObjectID) error {
	// Check if subscription exists
	subscription, err := s.repo.Subscriptions().Find(ctx, id)
	if err != nil {
		return err
	}

	// Check if subscription has payments
	payments, err := s.repo.Payments().FindBySubscription(ctx, id)
	if err != nil {
		return errors.New(errors.Service, "failed to check for subscription payments", errors.Internal, err)
	}

	if len(payments) > 0 {
		// Cancel instead of delete if payments exist
		subscription.Cancel("Subscription deleted by admin")
		return s.repo.Subscriptions().Update(ctx, subscription)
	}

	return s.repo.Subscriptions().Delete(ctx, id)
}

// Subscription management operations
func (s *service) CancelSubscription(ctx context.Context, subscriptionID primitive.ObjectID, reason string) error {
	subscription, err := s.repo.Subscriptions().Find(ctx, subscriptionID)
	if err != nil {
		return err
	}

	if subscription.Status == billing.SubscriptionStatusCancelled {
		return errors.New(errors.Service, "subscription is already cancelled", errors.Validation, nil)
	}

	if subscription.Status == billing.SubscriptionStatusExpired {
		return errors.New(errors.Service, "cannot cancel expired subscription", errors.Validation, nil)
	}

	subscription.Cancel(reason)
	return s.repo.Subscriptions().Update(ctx, subscription)
}

func (s *service) SuspendSubscription(ctx context.Context, subscriptionID primitive.ObjectID) error {
	subscription, err := s.repo.Subscriptions().Find(ctx, subscriptionID)
	if err != nil {
		return err
	}

	if subscription.Status == billing.SubscriptionStatusSuspended {
		return errors.New(errors.Service, "subscription is already suspended", errors.Validation, nil)
	}

	if subscription.Status == billing.SubscriptionStatusExpired || subscription.Status == billing.SubscriptionStatusCancelled {
		return errors.New(errors.Service, "cannot suspend expired or cancelled subscription", errors.Validation, nil)
	}

	subscription.Suspend()
	return s.repo.Subscriptions().Update(ctx, subscription)
}

func (s *service) ReactivateSubscription(ctx context.Context, subscriptionID primitive.ObjectID) error {
	subscription, err := s.repo.Subscriptions().Find(ctx, subscriptionID)
	if err != nil {
		return err
	}

	if subscription.Status != billing.SubscriptionStatusSuspended {
		return errors.New(errors.Service, "only suspended subscriptions can be reactivated", errors.Validation, nil)
	}

	// Check if subscription hasn't expired
	if time.Now().After(subscription.EndDate) {
		return errors.New(errors.Service, "cannot reactivate expired subscription", errors.Validation, nil)
	}

	subscription.Reactivate()
	return s.repo.Subscriptions().Update(ctx, subscription)
}

func (s *service) RenewSubscription(ctx context.Context, subscriptionID primitive.ObjectID) error {
	subscription, err := s.repo.Subscriptions().Find(ctx, subscriptionID)
	if err != nil {
		return err
	}

	if !subscription.AutoRenew {
		return errors.New(errors.Service, "subscription is not set to auto-renew", errors.Validation, nil)
	}

	// Get the plan to determine renewal duration
	plan, err := s.repo.Plans().Find(ctx, subscription.PlanID)
	if err != nil {
		return err
	}

	if !plan.IsActive() {
		return errors.New(errors.Service, "cannot renew subscription for inactive plan", errors.Validation, nil)
	}

	// Extend the subscription
	subscription.EndDate = billing.SubscriptionHelperInstance.CalculateEndDate(subscription.EndDate, plan.DurationMonths)
	subscription.Status = billing.SubscriptionStatusActive
	subscription.TrialEndDate = nil // No trial for renewals

	return s.repo.Subscriptions().Update(ctx, subscription)
}

// Subscription lookup operations
func (s *service) FindSubscriptionByProviderID(ctx context.Context, provider billing.PaymentProvider, providerSubscriptionID string) (*billing.Subscription, error) {
	if providerSubscriptionID == "" {
		return nil, errors.New(errors.Service, "provider subscription ID is required", errors.Validation, nil)
	}

	return s.repo.Subscriptions().FindByProviderSubscriptionID(ctx, provider, providerSubscriptionID)
}

func (s *service) FindExpiringSubscriptions(ctx context.Context, days int) ([]*billing.Subscription, error) {
	if days < 0 {
		return nil, errors.New(errors.Service, "days must be non-negative", errors.Validation, nil)
	}

	return s.repo.Subscriptions().FindExpiring(ctx, days)
}

// Access control operations
func (s *service) HasActiveSubscription(ctx context.Context, userID primitive.ObjectID) (bool, error) {
	subscriptions, err := s.repo.Subscriptions().FindActiveByUser(ctx, userID)
	if err != nil {
		return false, err
	}

	// Check if any subscription is truly active (not just in database)
	for _, subscription := range subscriptions {
		if subscription.IsActive() {
			return true, nil
		}
	}

	return false, nil
}

func (s *service) GetUserFeatures(ctx context.Context, userID primitive.ObjectID) ([]string, error) {
	subscriptions, err := s.repo.Subscriptions().FindActiveByUser(ctx, userID)
	if err != nil {
		return nil, err
	}

	featuresMap := make(map[string]bool)

	// Collect features from all active subscriptions
	for _, subscription := range subscriptions {
		if subscription.IsActive() {
			plan, err := s.repo.Plans().Find(ctx, subscription.PlanID)
			if err != nil {
				continue // Skip if plan not found
			}

			for _, feature := range plan.Features {
				featuresMap[feature] = true
			}
		}
	}

	// Convert map to slice
	features := make([]string, 0, len(featuresMap))
	for feature := range featuresMap {
		features = append(features, feature)
	}

	return features, nil
}
