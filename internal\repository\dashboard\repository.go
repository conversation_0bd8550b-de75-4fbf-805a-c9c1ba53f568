package dashboard

import (
	"context"
	"time"

	"github.com/dsoplabs/dinbora-backend/internal/model/dashboard"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// Reader interface defines read operations for dashboard entities
type Reader interface {
	// Unified FinancialMap operations (RECOMMENDED)
	// This method returns all dashboard data in a single document from the unified collection
	FindFinancialMap(ctx context.Context, userID string) (*dashboard.FinancialMap, error)

	// Individual entity operations (SUPPORTED)
	// These methods query embedded documents within the unified FinancialMap collection
	// They are maintained for backward compatibility and convenience

	// IncomeSource operations - queries embedded documents in FinancialMap
	FindIncomeSource(ctx context.Context, id primitive.ObjectID) (*dashboard.IncomeSource, error)
	FindIncomeSources(ctx context.Context, userID string) ([]*dashboard.IncomeSource, error)

	// StrategicFund operations - queries embedded document in FinancialMap
	FindStrategicFund(ctx context.Context, userID string) (*dashboard.StrategicFund, error)

	// Investment operations - queries embedded documents in FinancialMap
	FindInvestment(ctx context.Context, id primitive.ObjectID) (*dashboard.Investment, error)
	FindInvestments(ctx context.Context, userID string) ([]*dashboard.Investment, error)

	// Asset operations - queries embedded documents in FinancialMap
	FindAsset(ctx context.Context, id primitive.ObjectID) (*dashboard.Asset, error)
	FindAssets(ctx context.Context, userID string) ([]*dashboard.Asset, error)

	// NetWorthSnapshot operations - queries embedded documents in FinancialMap
	FindNetWorthHistory(ctx context.Context, userID string, limit int) ([]*dashboard.NetWorthSnapshot, error)
	FindLatestNetWorthSnapshot(ctx context.Context) (time.Time, error)

	// FinancialIndependence operations - queries standalone collection
	FindFinancialIndependence(ctx context.Context, userID string) (*dashboard.FinancialIndependence, error)
	FindFinancialIndependenceByUser(ctx context.Context, userID string) (*dashboard.FinancialIndependence, error)
}

// Writer interface defines write operations for dashboard entities
type Writer interface {
	// Unified FinancialMap operations (RECOMMENDED)
	// Use these methods for all new implementations
	SaveFinancialMap(ctx context.Context, financialMap *dashboard.FinancialMap) error
	UpdateFinancialMap(ctx context.Context, financialMap *dashboard.FinancialMap) error

	// Individual entity operations (DEPRECATED)
	// These methods are kept for interface compatibility but return NotImplemented errors
	// Use UpdateFinancialMap instead for all CRUD operations on embedded entities

	// IncomeSource operations (DEPRECATED - use UpdateFinancialMap)
	CreateIncomeSource(ctx context.Context, incomeSource *dashboard.IncomeSource) error
	UpdateIncomeSource(ctx context.Context, incomeSource *dashboard.IncomeSource) error
	DeleteIncomeSource(ctx context.Context, id primitive.ObjectID) error

	// StrategicFund operations (DEPRECATED - use UpdateFinancialMap)
	CreateStrategicFund(ctx context.Context, strategicFund *dashboard.StrategicFund) error
	UpdateStrategicFund(ctx context.Context, strategicFund *dashboard.StrategicFund) error

	// Investment operations (DEPRECATED - use UpdateFinancialMap)
	CreateInvestment(ctx context.Context, investment *dashboard.Investment) error
	UpdateInvestment(ctx context.Context, investment *dashboard.Investment) error
	DeleteInvestment(ctx context.Context, id primitive.ObjectID) error

	// Asset operations (DEPRECATED - use UpdateFinancialMap)
	CreateAsset(ctx context.Context, asset *dashboard.Asset) error
	UpdateAsset(ctx context.Context, asset *dashboard.Asset) error
	DeleteAsset(ctx context.Context, id primitive.ObjectID) error

	// NetWorthSnapshot operations (DEPRECATED - use UpdateFinancialMap)
	SaveNetWorthSnapshot(ctx context.Context, snapshot *dashboard.NetWorthSnapshot) error

	// FinancialIndependence operations - queries standalone collection
	CreateFinancialIndependence(ctx context.Context, fi *dashboard.FinancialIndependence) error
	UpdateFinancialIndependence(ctx context.Context, fi *dashboard.FinancialIndependence) error
	DeleteFinancialIndependence(ctx context.Context, id primitive.ObjectID) error
}

// Repository interface combines Reader and Writer interfaces
type Repository interface {
	Reader
	Writer
}
