package billing

import (
	"context"
	"log"

	"github.com/dsoplabs/dinbora-backend/internal/errors"
	"github.com/dsoplabs/dinbora-backend/internal/model/billing"
	"github.com/dsoplabs/dinbora-backend/internal/repository"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

type planMongoDB struct {
	collection *mongo.Collection
}

func NewPlanRepository(db *mongo.Database) PlanRepository {
	repo := &planMongoDB{
		collection: db.Collection(repository.BILLING_PLANS_COLLECTION),
	}

	// Create indexes
	repo.createIndexes()

	return repo
}

func (r *planMongoDB) createIndexes() {
	ctx := context.Background()

	// Index on status for finding active plans
	_, err := r.collection.Indexes().CreateOne(
		ctx,
		mongo.IndexModel{
			Keys:    bson.D{{Key: "status", Value: 1}},
			Options: options.Index().SetName("planStatus"),
		},
	)
	if err != nil {
		log.Printf("warning: failed to create index on plans.status field: %v", err)
	}

	// Index on hotmartProductId for webhook lookups
	_, err = r.collection.Indexes().CreateOne(
		ctx,
		mongo.IndexModel{
			Keys:    bson.D{{Key: "hotmartProductId", Value: 1}},
			Options: options.Index().SetName("planHotmartProductId").SetSparse(true),
		},
	)
	if err != nil {
		log.Printf("warning: failed to create index on plans.hotmartProductId field: %v", err)
	}

	// Index on appleProductId for webhook lookups
	_, err = r.collection.Indexes().CreateOne(
		ctx,
		mongo.IndexModel{
			Keys:    bson.D{{Key: "appleProductId", Value: 1}},
			Options: options.Index().SetName("planAppleProductId").SetSparse(true),
		},
	)
	if err != nil {
		log.Printf("warning: failed to create index on plans.appleProductId field: %v", err)
	}
}

// Read operations
func (r *planMongoDB) Find(ctx context.Context, id primitive.ObjectID) (*billing.Plan, error) {
	var plan billing.Plan
	err := r.collection.FindOne(ctx, bson.M{"_id": id}).Decode(&plan)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return nil, errors.New(errors.Repository, "plan not found", errors.NotFound, err)
		}
		return nil, errors.New(errors.Repository, "failed to find plan", errors.Internal, err)
	}

	plan.ID = plan.ObjectID.Hex()
	return &plan, nil
}

func (r *planMongoDB) FindByHotmartProductID(ctx context.Context, hotmartProductID string) (*billing.Plan, error) {
	var plan billing.Plan
	err := r.collection.FindOne(ctx, bson.M{"hotmartProductId": hotmartProductID}).Decode(&plan)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return nil, errors.New(errors.Repository, "plan not found", errors.NotFound, err)
		}
		return nil, errors.New(errors.Repository, "failed to find plan by hotmart product ID", errors.Internal, err)
	}

	plan.ID = plan.ObjectID.Hex()
	return &plan, nil
}

func (r *planMongoDB) FindByAppleProductID(ctx context.Context, appleProductID string) (*billing.Plan, error) {
	var plan billing.Plan
	err := r.collection.FindOne(ctx, bson.M{"appleProductId": appleProductID}).Decode(&plan)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return nil, errors.New(errors.Repository, "plan not found", errors.NotFound, err)
		}
		return nil, errors.New(errors.Repository, "failed to find plan by apple product ID", errors.Internal, err)
	}

	plan.ID = plan.ObjectID.Hex()
	return &plan, nil
}

func (r *planMongoDB) FindAll(ctx context.Context) ([]*billing.Plan, error) {
	cursor, err := r.collection.Find(ctx, bson.M{})
	if err != nil {
		return nil, errors.New(errors.Repository, "failed to find plans", errors.Internal, err)
	}
	defer cursor.Close(ctx)

	var plans []*billing.Plan
	for cursor.Next(ctx) {
		var plan billing.Plan
		if err := cursor.Decode(&plan); err != nil {
			return nil, errors.New(errors.Repository, "failed to decode plan", errors.Internal, err)
		}
		plan.ID = plan.ObjectID.Hex()
		plans = append(plans, &plan)
	}

	if err := cursor.Err(); err != nil {
		return nil, errors.New(errors.Repository, "cursor error while finding plans", errors.Internal, err)
	}

	return plans, nil
}

func (r *planMongoDB) FindActive(ctx context.Context) ([]*billing.Plan, error) {
	cursor, err := r.collection.Find(ctx, bson.M{"status": billing.PlanStatusActive})
	if err != nil {
		return nil, errors.New(errors.Repository, "failed to find active plans", errors.Internal, err)
	}
	defer cursor.Close(ctx)

	var plans []*billing.Plan
	for cursor.Next(ctx) {
		var plan billing.Plan
		if err := cursor.Decode(&plan); err != nil {
			return nil, errors.New(errors.Repository, "failed to decode plan", errors.Internal, err)
		}
		plan.ID = plan.ObjectID.Hex()
		plans = append(plans, &plan)
	}

	if err := cursor.Err(); err != nil {
		return nil, errors.New(errors.Repository, "cursor error while finding active plans", errors.Internal, err)
	}

	return plans, nil
}

// Write operations
func (r *planMongoDB) Create(ctx context.Context, plan *billing.Plan) (string, error) {
	plan.SetDefaults()
	
	if err := plan.Validate(); err != nil {
		return "", err
	}

	result, err := r.collection.InsertOne(ctx, plan)
	if err != nil {
		return "", errors.New(errors.Repository, "failed to create plan", errors.Internal, err)
	}

	objectID, ok := result.InsertedID.(primitive.ObjectID)
	if !ok {
		return "", errors.New(errors.Repository, "failed to get inserted plan ID", errors.Internal, nil)
	}

	return objectID.Hex(), nil
}

func (r *planMongoDB) Update(ctx context.Context, plan *billing.Plan) error {
	plan.SetDefaults()
	
	if err := plan.Validate(); err != nil {
		return err
	}

	filter := bson.M{"_id": plan.ObjectID}
	update := bson.M{"$set": plan}

	result, err := r.collection.UpdateOne(ctx, filter, update)
	if err != nil {
		return errors.New(errors.Repository, "failed to update plan", errors.Internal, err)
	}

	if result.MatchedCount == 0 {
		return errors.New(errors.Repository, "plan not found", errors.NotFound, nil)
	}

	return nil
}

func (r *planMongoDB) Delete(ctx context.Context, id primitive.ObjectID) error {
	result, err := r.collection.DeleteOne(ctx, bson.M{"_id": id})
	if err != nil {
		return errors.New(errors.Repository, "failed to delete plan", errors.Internal, err)
	}

	if result.DeletedCount == 0 {
		return errors.New(errors.Repository, "plan not found", errors.NotFound, nil)
	}

	return nil
}
