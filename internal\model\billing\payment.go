package billing

import (
	"encoding/json"
	"time"

	"github.com/dsoplabs/dinbora-backend/internal/errors"
	"github.com/dsoplabs/dinbora-backend/internal/model/monetary"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// Payment records every transaction event from payment providers for auditing and support
type Payment struct {
	ObjectID primitive.ObjectID `json:"-" bson:"_id,omitempty"`
	ID       string             `json:"id,omitempty" bson:"-"`

	// Core relationships
	UserID         primitive.ObjectID  `json:"userId" bson:"userId" validate:"required"`
	SubscriptionID *primitive.ObjectID `json:"subscriptionId,omitempty" bson:"subscriptionId,omitempty"`

	// Payment provider information
	Provider              PaymentProvider `json:"provider" bson:"provider" validate:"required"`
	ProviderTransactionID string          `json:"providerTransactionId" bson:"providerTransactionId" validate:"required"`

	// Payment details
	Amount   monetary.Amount `json:"amount" bson:"amount" validate:"required,min=0"`
	Currency string          `json:"currency" bson:"currency" validate:"required,len=3"`

	// Payment status and type
	Status PaymentStatus `json:"status" bson:"status" validate:"required"`
	Type   PaymentType   `json:"type" bson:"type" validate:"required"`

	// Webhook data for auditing and debugging
	WebhookData json.RawMessage `json:"webhookData,omitempty" bson:"webhookData,omitempty"`

	// Processing information
	ProcessedAt *time.Time `json:"processedAt,omitempty" bson:"processedAt,omitempty"`
	FailureReason string   `json:"failureReason,omitempty" bson:"failureReason,omitempty"`

	// Metadata
	CreatedAt time.Time `json:"createdAt" bson:"createdAt"`
	UpdatedAt time.Time `json:"updatedAt" bson:"updatedAt"`
}

// PaymentStatus represents the status of a payment
type PaymentStatus string

const (
	PaymentStatusPending   PaymentStatus = "pending"
	PaymentStatusCompleted PaymentStatus = "completed"
	PaymentStatusFailed    PaymentStatus = "failed"
	PaymentStatusRefunded  PaymentStatus = "refunded"
	PaymentStatusCancelled PaymentStatus = "cancelled"
)

// PaymentType represents the type of payment
type PaymentType string

const (
	PaymentTypeSubscription PaymentType = "subscription"
	PaymentTypeRenewal      PaymentType = "renewal"
	PaymentTypeRefund       PaymentType = "refund"
	PaymentTypeChargeback   PaymentType = "chargeback"
)

// IsValid validates the payment status
func (ps PaymentStatus) IsValid() bool {
	switch ps {
	case PaymentStatusPending, PaymentStatusCompleted, PaymentStatusFailed, 
		 PaymentStatusRefunded, PaymentStatusCancelled:
		return true
	default:
		return false
	}
}

// IsValid validates the payment type
func (pt PaymentType) IsValid() bool {
	switch pt {
	case PaymentTypeSubscription, PaymentTypeRenewal, PaymentTypeRefund, PaymentTypeChargeback:
		return true
	default:
		return false
	}
}

// Validate validates all fields in the payment
func (p *Payment) Validate() error {
	if p.UserID.IsZero() {
		return errors.New(errors.Model, "payment userID is required", errors.Validation, nil)
	}

	if !p.Provider.IsValid() {
		return errors.New(errors.Model, "invalid payment provider", errors.Validation, nil)
	}

	if len(p.ProviderTransactionID) == 0 {
		return errors.New(errors.Model, "provider transaction ID is required", errors.Validation, nil)
	}

	if p.Amount < 0 {
		return errors.New(errors.Model, "payment amount cannot be negative", errors.Validation, nil)
	}

	if len(p.Currency) != 3 {
		return errors.New(errors.Model, "currency must be a 3-character ISO code", errors.Validation, nil)
	}

	if !p.Status.IsValid() {
		return errors.New(errors.Model, "invalid payment status", errors.Validation, nil)
	}

	if !p.Type.IsValid() {
		return errors.New(errors.Model, "invalid payment type", errors.Validation, nil)
	}

	return nil
}

// SetDefaults sets default values for the payment
func (p *Payment) SetDefaults() {
	now := time.Now()
	
	if p.CreatedAt.IsZero() {
		p.CreatedAt = now
	}
	p.UpdatedAt = now

	if p.Currency == "" {
		p.Currency = "BRL" // Default currency for Brazilian market
	}

	if p.Status == "" {
		p.Status = PaymentStatusPending
	}

	if p.Type == "" {
		p.Type = PaymentTypeSubscription
	}
}

// MarkAsCompleted marks the payment as completed
func (p *Payment) MarkAsCompleted() {
	now := time.Now()
	p.Status = PaymentStatusCompleted
	p.ProcessedAt = &now
	p.UpdatedAt = now
	p.FailureReason = "" // Clear any previous failure reason
}

// MarkAsFailed marks the payment as failed with a reason
func (p *Payment) MarkAsFailed(reason string) {
	now := time.Now()
	p.Status = PaymentStatusFailed
	p.ProcessedAt = &now
	p.FailureReason = reason
	p.UpdatedAt = now
}

// MarkAsRefunded marks the payment as refunded
func (p *Payment) MarkAsRefunded() {
	now := time.Now()
	p.Status = PaymentStatusRefunded
	p.ProcessedAt = &now
	p.UpdatedAt = now
}

// MarkAsCancelled marks the payment as cancelled
func (p *Payment) MarkAsCancelled() {
	now := time.Now()
	p.Status = PaymentStatusCancelled
	p.ProcessedAt = &now
	p.UpdatedAt = now
}

// IsCompleted checks if the payment is completed
func (p *Payment) IsCompleted() bool {
	return p.Status == PaymentStatusCompleted
}

// IsPending checks if the payment is pending
func (p *Payment) IsPending() bool {
	return p.Status == PaymentStatusPending
}

// IsFailed checks if the payment failed
func (p *Payment) IsFailed() bool {
	return p.Status == PaymentStatusFailed
}

// IsRefunded checks if the payment was refunded
func (p *Payment) IsRefunded() bool {
	return p.Status == PaymentStatusRefunded
}

// SetWebhookData sets the webhook data from raw JSON
func (p *Payment) SetWebhookData(data interface{}) error {
	webhookBytes, err := json.Marshal(data)
	if err != nil {
		return errors.New(errors.Model, "failed to marshal webhook data", errors.Internal, err)
	}
	p.WebhookData = webhookBytes
	return nil
}

// GetWebhookData unmarshals the webhook data into the provided interface
func (p *Payment) GetWebhookData(v interface{}) error {
	if len(p.WebhookData) == 0 {
		return errors.New(errors.Model, "no webhook data available", errors.NotFound, nil)
	}
	
	err := json.Unmarshal(p.WebhookData, v)
	if err != nil {
		return errors.New(errors.Model, "failed to unmarshal webhook data", errors.Internal, err)
	}
	return nil
}
