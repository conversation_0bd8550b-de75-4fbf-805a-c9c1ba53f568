package financialsheet

import (
	"time"

	"github.com/dsoplabs/dinbora-backend/internal/model/financialsheet"
	"github.com/dsoplabs/dinbora-backend/internal/model/monetary"
)

type DreamTransactionRequest struct {
	DreamID       string                       `json:"dreamId" validate:"required"`
	Value         monetary.Amount              `json:"value" validate:"required,min=1"`
	Date          time.Time                    `json:"date" validate:"required"`
	PaymentMethod financialsheet.PaymentMethod `json:"paymentMethod" validate:"required"`
}
